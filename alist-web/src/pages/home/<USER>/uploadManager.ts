import { createSignal, createEffect } from "solid-js"
import { createStore } from "solid-js/store"
import { UploadFileProps, UploadStats } from "./types"
import { bus } from "~/utils"

// 上传管理器
class UploadManager {
  private uploadsStore = createStore<UploadFileProps[]>([])
  private uploads = this.uploadsStore[0]
  private setUploads = this.uploadsStore[1]

  private statsStore = createStore<UploadStats>({
    total: 0,
    completed: 0,
    failed: 0,
    uploading: 0,
    pending: 0,
    paused: 0,
    cancelled: 0,
    totalSize: 0,
    uploadedSize: 0,
    totalSpeed: 0,
  })
  private stats = this.statsStore[0]
  private setStats = this.statsStore[1]

  private visibilitySignal = createSignal(false)
  private isVisible = this.visibilitySignal[0]
  private setIsVisible = this.visibilitySignal[1]

  constructor() {
    // 监听上传状态变化，自动更新统计信息
    createEffect(() => {
      this.updateStats()
    })

    // 监听全局事件
    bus.on("upload_panel_show", () => this.show())
    bus.on("upload_panel_hide", () => this.hide())
  }

  // 获取上传列表
  getUploads() {
    return this.uploads
  }

  // 获取统计信息
  getStats() {
    return this.stats
  }

  // 获取面板可见性
  getVisibility() {
    return this.isVisible()
  }

  // 显示面板
  show() {
    this.setIsVisible(true)
  }

  // 隐藏面板
  hide() {
    this.setIsVisible(false)
  }

  // 添加上传任务
  addUpload(upload: Omit<UploadFileProps, "id" | "startTime">) {
    const newUpload: UploadFileProps = {
      ...upload,
      id: this.generateId(),
      startTime: Date.now(),
      retryCount: 0,
    }
    
    this.setUploads(prev => [...prev, newUpload])
    this.show() // 自动显示面板
    this.saveToStorage()

    return newUpload.id
  }

  // 更新上传任务
  updateUpload(id: string, updates: Partial<UploadFileProps>) {
    this.setUploads(prev => prev.map(upload =>
      upload.id === id ? { ...upload, ...updates } : upload
    ))
    this.saveToStorage()
  }

  // 删除上传任务
  removeUpload(id: string) {
    this.setUploads(prev => prev.filter(upload => upload.id !== id))
    this.saveToStorage()
  }

  // 暂停上传
  pauseUpload(id: string) {
    this.updateUpload(id, { status: "paused" })
    bus.emit("upload_pause", id)
  }

  // 恢复上传
  resumeUpload(id: string) {
    this.updateUpload(id, { status: "uploading" })
    bus.emit("upload_resume", id)
  }

  // 取消上传
  cancelUpload(id: string) {
    this.updateUpload(id, { status: "cancelled", endTime: Date.now() })
    bus.emit("upload_cancel", id)
  }

  // 重试上传
  retryUpload(id: string) {
    const upload = this.uploads.find(u => u.id === id)
    if (upload) {
      this.updateUpload(id, {
        status: "pending",
        progress: 0,
        msg: undefined,
        retryCount: (upload.retryCount || 0) + 1,
        startTime: Date.now(),
        endTime: undefined,
      })
      bus.emit("upload_retry", id)
    }
  }

  // 暂停所有上传
  pauseAll() {
    this.uploads.forEach(upload => {
      if (upload.status === "uploading") {
        this.pauseUpload(upload.id)
      }
    })
  }

  // 恢复所有上传
  resumeAll() {
    this.uploads.forEach(upload => {
      if (upload.status === "paused") {
        this.resumeUpload(upload.id)
      }
    })
  }

  // 重试失败的上传
  retryFailed() {
    this.uploads.forEach(upload => {
      if (upload.status === "error") {
        this.retryUpload(upload.id)
      }
    })
  }

  // 清除已完成的上传
  clearCompleted() {
    this.setUploads(prev => prev.filter(upload =>
      !["success", "cancelled"].includes(upload.status)
    ))
    this.saveToStorage()
  }

  // 清除所有上传
  clearAll() {
    this.setUploads([])
    this.saveToStorage()
  }

  // 更新统计信息
  private updateStats() {
    const uploads = this.uploads
    const newStats: UploadStats = {
      total: uploads.length,
      completed: uploads.filter(u => u.status === "success").length,
      failed: uploads.filter(u => u.status === "error").length,
      uploading: uploads.filter(u => u.status === "uploading").length,
      pending: uploads.filter(u => u.status === "pending").length,
      paused: uploads.filter(u => u.status === "paused").length,
      cancelled: uploads.filter(u => u.status === "cancelled").length,
      totalSize: uploads.reduce((sum, u) => sum + u.size, 0),
      uploadedSize: uploads.reduce((sum, u) => sum + (u.size * u.progress / 100), 0),
      totalSpeed: uploads
        .filter(u => u.status === "uploading")
        .reduce((sum, u) => sum + u.speed, 0),
    }

    this.setStats(newStats)
  }

  // 生成唯一ID
  private generateId(): string {
    return `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // 保存到本地存储
  private saveToStorage() {
    try {
      const data = {
        uploads: this.uploads,
        timestamp: Date.now(),
      }
      localStorage.setItem("alist_upload_queue", JSON.stringify(data))
    } catch (error) {
      console.warn("Failed to save upload queue to localStorage:", error)
    }
  }

  // 从本地存储恢复
  loadFromStorage() {
    try {
      const data = localStorage.getItem("alist_upload_queue")
      if (data) {
        const parsed = JSON.parse(data)
        // 只恢复未完成的上传任务
        const validUploads = parsed.uploads.filter((upload: UploadFileProps) => 
          !["success", "cancelled"].includes(upload.status)
        )
        
        // 将正在上传的任务标记为暂停状态
        const restoredUploads = validUploads.map((upload: UploadFileProps) => ({
          ...upload,
          status: upload.status === "uploading" ? "paused" : upload.status,
          speed: 0,
        }))
        
        this.setUploads(restoredUploads)
        
        if (restoredUploads.length > 0) {
          this.show()
        }
      }
    } catch (error) {
      console.warn("Failed to load upload queue from localStorage:", error)
    }
  }

  // 获取上传任务
  getUpload(id: string): UploadFileProps | undefined {
    return this.uploads.find(u => u.id === id)
  }

  // 检查是否有正在进行的上传
  hasActiveUploads(): boolean {
    return this.uploads.some(u => ["uploading", "pending"].includes(u.status))
  }

  // 获取按状态分组的上传任务
  getUploadsByStatus() {
    return {
      uploading: this.uploads.filter(u => u.status === "uploading"),
      pending: this.uploads.filter(u => u.status === "pending"),
      paused: this.uploads.filter(u => u.status === "paused"),
      success: this.uploads.filter(u => u.status === "success"),
      error: this.uploads.filter(u => u.status === "error"),
      cancelled: this.uploads.filter(u => u.status === "cancelled"),
    }
  }
}

// 创建全局上传管理器实例
export const uploadManager = new UploadManager()

// 在应用启动时恢复上传队列
uploadManager.loadFromStorage()
