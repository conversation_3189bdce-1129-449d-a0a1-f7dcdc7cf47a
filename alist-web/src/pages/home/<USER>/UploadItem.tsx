import {
  VStack,
  HStack,
  Text,
  Badge,
  Progress,
  ProgressIndicator,
  IconButton,
  Box,
  Tooltip,
  Image,
} from "@hope-ui/solid"
import { Show, createMemo } from "solid-js"
import { useT } from "~/hooks"
import { getMainColor } from "~/store"
import { UploadFileProps, StatusBadge } from "./types"
import {
  RiMediaPauseFill,
  RiMediaPlayFill,
  RiSystemCloseLine,
  RiSystemRefreshLine,
} from "solid-icons/ri"
import {
  BsFileEarmarkTextFill,
  BsFileEarmarkImageFill,
  BsFileEarmarkPlayFill,
  BsFileEarmarkMusicFill,
  BsFileEarmarkZipFill,
  BsFileEarmarkFill,
} from "solid-icons/bs"
import { getFileSize } from "~/utils"

interface UploadItemProps extends UploadFileProps {
  onPause?: () => void
  onResume?: () => void
  onCancel?: () => void
  onRetry?: () => void
}

export const UploadItem = (props: UploadItemProps) => {
  const t = useT()

  // 获取文件图标
  const getFileIcon = () => {
    const ext = props.name.split('.').pop()?.toLowerCase() || ''

    // 图片文件
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(ext)) {
      return <BsFileEarmarkImageFill color="$success9" />
    }

    // 视频文件
    if (['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm'].includes(ext)) {
      return <BsFileEarmarkPlayFill color="$info9" />
    }

    // 音频文件
    if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma'].includes(ext)) {
      return <BsFileEarmarkMusicFill color="$warning9" />
    }

    // 压缩文件
    if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2'].includes(ext)) {
      return <BsFileEarmarkZipFill color="$accent9" />
    }

    // 文档文件
    if (['txt', 'doc', 'docx', 'pdf', 'xls', 'xlsx', 'ppt', 'pptx'].includes(ext)) {
      return <BsFileEarmarkTextFill color="$neutral9" />
    }

    // 默认文件图标
    return <BsFileEarmarkFill color="$neutral8" />
  }

  // 格式化文件大小
  const formatFileSize = createMemo(() => {
    return getFileSize(props.size)
  })

  // 格式化上传速度
  const formatSpeed = createMemo(() => {
    if (props.status !== "uploading" || props.speed === 0) return ""
    return `${getFileSize(props.speed)}/s`
  })

  // 计算剩余时间
  const estimatedTime = createMemo(() => {
    if (props.status !== "uploading" || props.speed === 0) return ""
    const remainingBytes = props.size * (100 - props.progress) / 100
    const remainingSeconds = Math.round(remainingBytes / props.speed)
    
    if (remainingSeconds < 60) return `${remainingSeconds}s`
    if (remainingSeconds < 3600) return `${Math.round(remainingSeconds / 60)}m`
    return `${Math.round(remainingSeconds / 3600)}h`
  })

  // 获取状态文本
  const getStatusText = () => {
    switch (props.status) {
      case "pending":
        return t("home.upload.pending")
      case "uploading":
        return t("home.upload.uploading")
      case "backending":
        return t("home.upload.backending")
      case "success":
        return t("home.upload.success")
      case "error":
        return t("home.upload.error")
      case "paused":
        return t("common.paused")
      case "cancelled":
        return t("common.cancelled")
      default:
        return props.status
    }
  }

  // 获取操作按钮
  const getActionButtons = () => {
    const buttons = []

    if (props.status === "uploading" && props.onPause) {
      buttons.push(
        <Tooltip label={t("common.pause")}>
          <IconButton
            aria-label="pause"
            size="xs"
            variant="ghost"
            icon={<RiMediaPauseFill />}
            onClick={props.onPause}
          />
        </Tooltip>
      )
    }

    if (props.status === "paused" && props.onResume) {
      buttons.push(
        <Tooltip label={t("common.resume")}>
          <IconButton
            aria-label="resume"
            size="xs"
            variant="ghost"
            icon={<RiMediaPlayFill />}
            onClick={props.onResume}
          />
        </Tooltip>
      )
    }

    if (props.status === "error" && props.onRetry) {
      buttons.push(
        <Tooltip label={t("common.retry")}>
          <IconButton
            aria-label="retry"
            size="xs"
            variant="ghost"
            icon={<RiSystemRefreshLine />}
            onClick={props.onRetry}
          />
        </Tooltip>
      )
    }

    if (["pending", "uploading", "paused", "error"].includes(props.status) && props.onCancel) {
      buttons.push(
        <Tooltip label={t("common.cancel")}>
          <IconButton
            aria-label="cancel"
            size="xs"
            variant="ghost"
            icon={<RiSystemCloseLine />}
            onClick={props.onCancel}
            color="$danger9"
          />
        </Tooltip>
      )
    }

    return buttons
  }

  return (
    <VStack
      w="$full"
      spacing="$2"
      rounded="$md"
      border="1px solid $neutral6"
      alignItems="start"
      p="$3"
      bg="$loContrast"
      _hover={{
        border: `1px solid ${getMainColor()}`,
        bg: "$neutral2",
      }}
      css={{
        transition: "all 0.2s ease",
      }}
    >
      {/* 文件信息行 */}
      <HStack w="$full" justifyContent="space-between" alignItems="start">
        <HStack spacing="$2" alignItems="center" flex="1" minW="0">
          {/* 文件图标 */}
          <Box fontSize="$lg" flexShrink="0">
            {getFileIcon()}
          </Box>
          
          {/* 文件名和路径 */}
          <VStack spacing="$1" alignItems="start" flex="1" minW="0">
            <Text
              fontSize="$sm"
              fontWeight="$medium"
              noOfLines={1}
              css={{
                wordBreak: "break-all",
              }}
            >
              {props.name}
            </Text>
            <Show when={props.path !== props.name}>
              <Text
                fontSize="$xs"
                color="$neutral10"
                noOfLines={1}
                css={{
                  wordBreak: "break-all",
                }}
              >
                {props.path}
              </Text>
            </Show>
          </VStack>
        </HStack>

        {/* 操作按钮 */}
        <HStack spacing="$1" flexShrink="0">
          {getActionButtons()}
        </HStack>
      </HStack>

      {/* 状态和大小信息 */}
      <HStack w="$full" justifyContent="space-between" alignItems="center">
        <HStack spacing="$2" alignItems="center">
          <Badge colorScheme={StatusBadge[props.status]} size="sm">
            {getStatusText()}
          </Badge>
          <Text fontSize="$xs" color="$neutral10">
            {formatFileSize()}
          </Text>
        </HStack>

        <HStack spacing="$2" alignItems="center">
          <Show when={formatSpeed()}>
            <Text fontSize="$xs" color="$info10">
              {formatSpeed()}
            </Text>
          </Show>
          <Show when={estimatedTime()}>
            <Text fontSize="$xs" color="$neutral10">
              {estimatedTime()}
            </Text>
          </Show>
        </HStack>
      </HStack>

      {/* 进度条 */}
      <VStack w="$full" spacing="$1">
        <Progress
          w="$full"
          trackColor="$neutral4"
          rounded="$full"
          value={props.progress}
          size="sm"
        >
          <ProgressIndicator 
            color={
              props.status === "error" ? "$danger9" :
              props.status === "success" ? "$success9" :
              props.status === "paused" ? "$warning9" :
              getMainColor()
            } 
            rounded="$full" 
          />
        </Progress>
        
        <HStack w="$full" justifyContent="space-between">
          <Text fontSize="$xs" color="$neutral10">
            {props.progress.toFixed(1)}%
          </Text>
          <Show when={props.status === "uploading"}>
            <Text fontSize="$xs" color="$neutral10">
              {getFileSize(props.size * props.progress / 100)} / {formatFileSize()}
            </Text>
          </Show>
        </HStack>
      </VStack>

      {/* 错误信息 */}
      <Show when={props.msg && props.status === "error"}>
        <Box
          w="$full"
          p="$2"
          bg="$danger2"
          border="1px solid $danger6"
          borderRadius="$sm"
        >
          <Text fontSize="$xs" color="$danger11" css={{ wordBreak: "break-word" }}>
            {props.msg}
          </Text>
        </Box>
      </Show>

      {/* 重试次数显示 */}
      <Show when={props.retryCount && props.retryCount > 0}>
        <Text fontSize="$xs" color="$warning10">
          {t("common.retry_count")}: {props.retryCount}
        </Text>
      </Show>
    </VStack>
  )
}
