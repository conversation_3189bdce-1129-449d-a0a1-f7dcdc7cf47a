type Status = "pending" | "uploading" | "backending" | "success" | "error" | "paused" | "cancelled"

export interface UploadFileProps {
  id: string // 添加唯一ID
  name: string
  path: string
  size: number
  progress: number
  speed: number
  status: Status
  msg?: string
  startTime?: number // 开始时间
  endTime?: number // 结束时间
  retryCount?: number // 重试次数
}

export const StatusBadge = {
  pending: "neutral",
  uploading: "info",
  backending: "info",
  success: "success",
  error: "danger",
  paused: "warning",
  cancelled: "neutral",
} as const

// 上传统计信息
export interface UploadStats {
  total: number
  completed: number
  failed: number
  uploading: number
  pending: number
  paused: number
  cancelled: number
  totalSize: number
  uploadedSize: number
  totalSpeed: number
}

// 浮动面板状态
export interface PanelState {
  isMinimized: boolean
  isVisible: boolean
  position: { x: number; y: number }
  size: { width: number; height: number }
}

export type SetUpload = (key: keyof UploadFileProps, value: any) => void
export type Upload = (
  uploadPath: string,
  file: File,
  setUpload: SetUpload,
  asTask: boolean,
  overwrite: boolean,
  rapid: boolean,
) => Promise<Error | undefined>
