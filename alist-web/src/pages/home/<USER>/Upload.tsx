import {
  VStack,
  Input,
  Heading,
  HStack,
  IconButton,
  Checkbox,
  Text,
  Badge,
  Progress,
  ProgressIndicator,
  Button,
  Box,
} from "@hope-ui/solid"
import { createSignal, For, Show, createMemo, onMount } from "solid-js"
import { usePath, useRouter, useT } from "~/hooks"
import { getMainColor, me } from "~/store"
import { UserMethods } from "~/types"
import {
  RiDocumentFolderUploadFill,
  RiDocumentFileUploadFill,
} from "solid-icons/ri"
import { getFileSize, notify, pathJoin, bus } from "~/utils"
import { asyncPool } from "~/utils/async_pool"
import { createStore } from "solid-js/store"
import { UploadFileProps, StatusBadge } from "./types"
import { File2Upload, traverseFileTree } from "./util"
import { SelectWrapper, UploadPanel } from "~/components"
import { getUploads } from "./uploads"
import { uploadManager } from "./uploadManager"
import { UploadItem } from "./UploadItem"

// 移除旧的UploadFile组件，使用新的UploadItem组件

const Upload = () => {
  const t = useT()
  const { pathname } = useRouter()
  const { refresh } = usePath()
  const [drag, setDrag] = createSignal(false)
  const [asTask, setAsTask] = createSignal(false)
  const [overwrite, setOverwrite] = createSignal(false)
  const [rapid, setRapid] = createSignal(true)
  let fileInput: HTMLInputElement
  let folderInput: HTMLInputElement

  // 监听上传管理器状态
  const panelUploads = () => uploadManager.getUploads()
  const panelStats = () => uploadManager.getStats()
  const isPanelVisible = () => uploadManager.getVisibility()

  // 检查是否为普通用户在根目录
  const isRootForNormalUser = createMemo(() => {
    const user = me()
    const path = pathname()
    return !UserMethods.is_admin(user) && (path === "/" || path === "")
  })

  // 组件挂载时初始化
  onMount(() => {
    // 监听上传相关事件
    bus.on("upload_start", () => {
      uploadManager.show()
    })
  })

  const handleAddFiles = async (files: File[]) => {
    if (files.length === 0) return

    // 检查普通用户是否在根目录上传
    if (isRootForNormalUser()) {
      notify.error("当前目录不支持创建文件！")
      return
    }

    // 添加文件到上传管理器
    const uploadIds: string[] = []
    for (const file of files) {
      const upload = File2Upload(file)
      const uploadId = uploadManager.addUpload(upload)
      uploadIds.push(uploadId)
    }

    // 开始上传处理
    for await (const ms of asyncPool(3, files, (file, index) =>
      handleFile(file, uploadIds[index])
    )) {
      console.log(ms)
    }

    refresh(undefined, true)
  }
  const uploaders = getUploads()
  const [curUploader, setCurUploader] = createSignal(uploaders[0])

  const handleFile = async (file: File, uploadId: string) => {
    const path = file.webkitRelativePath ? file.webkitRelativePath : file.name
    const uploadPath = pathJoin(pathname(), path)

    // 更新上传状态为正在上传
    uploadManager.updateUpload(uploadId, { status: "uploading" })

    try {
      const err = await curUploader().upload(
        uploadPath,
        file,
        (key, value) => {
          // 更新上传管理器中的进度
          uploadManager.updateUpload(uploadId, { [key]: value })
        },
        asTask(),
        overwrite(),
        rapid(),
      )

      if (!err) {
        uploadManager.updateUpload(uploadId, {
          status: "success",
          progress: 100,
          endTime: Date.now()
        })
        // 触发文件上传事件，用于刷新容量信息
        bus.emit("file_uploaded")
      } else {
        uploadManager.updateUpload(uploadId, {
          status: "error",
          msg: err.message,
          endTime: Date.now()
        })
      }
    } catch (e: any) {
      console.error(e)
      uploadManager.updateUpload(uploadId, {
        status: "error",
        msg: e.message,
        endTime: Date.now()
      })
    }
  }
  return (
    <>
      {/* 浮动上传面板 */}
      <UploadPanel
        isVisible={isPanelVisible()}
        onClose={() => uploadManager.hide()}
        uploads={panelUploads()}
        stats={panelStats()}
        onPauseAll={() => uploadManager.pauseAll()}
        onResumeAll={() => uploadManager.resumeAll()}
        onClearCompleted={() => uploadManager.clearCompleted()}
        onRetryFailed={() => uploadManager.retryFailed()}
      />

      {/* 上传区域 */}
      <VStack w="$full" pb="$2" spacing="$2">
        <Input
          type="file"
          multiple
          ref={fileInput!}
          display="none"
          onChange={(e) => {
            // @ts-ignore
            handleAddFiles(Array.from(e.target.files ?? []))
          }}
        />
        <Input
          type="file"
          multiple
          // @ts-ignore
          webkitdirectory
          ref={folderInput!}
          display="none"
          onChange={(e) => {
            // @ts-ignore
            handleAddFiles(Array.from(e.target.files ?? []))
          }}
        />
        <VStack
          w="$full"
          justifyContent="center"
          border={`2px dashed ${drag() ? getMainColor() : "$neutral8"}`}
          rounded="$lg"
          onDragOver={(e: DragEvent) => {
            e.preventDefault()
            setDrag(true)
          }}
          onDragLeave={() => {
            setDrag(false)
          }}
          onDrop={async (e: DragEvent) => {
            e.preventDefault()
            e.stopPropagation()
            setDrag(false)
            const res: File[] = []
            const items = Array.from(e.dataTransfer?.items ?? [])
            const files = Array.from(e.dataTransfer?.files ?? [])
            let itemLength = items.length
            const folderEntries = []
            for (let i = 0; i < itemLength; i++) {
              const item = items[i]
              const entry = item.webkitGetAsEntry()
              if (entry?.isFile) {
                res.push(files[i])
              } else if (entry?.isDirectory) {
                folderEntries.push(entry)
              }
            }
            for (const entry of folderEntries) {
              const innerFiles = await traverseFileTree(entry)
              res.push(...innerFiles)
            }
            if (res.length === 0) {
              notify.warning(t("home.upload.no_files_drag"))
            }
            handleAddFiles(res)
          }}
          spacing="$4"
          // py="$4"
          h="$56"
        >
          <Show
            when={!drag()}
            fallback={<Heading>{t("home.upload.release")}</Heading>}
          >
            <Heading>{t("home.upload.upload-tips")}</Heading>
            <Box w="30%">
              <SelectWrapper
                value={curUploader().name}
                onChange={(name) => {
                  setCurUploader(
                    uploaders.find((uploader) => uploader.name === name)!,
                  )
                }}
                options={uploaders.map((uploader) => {
                  return {
                    label: uploader.name,
                    value: uploader.name,
                  }
                })}
              />
            </Box>
            <HStack spacing="$4">
              <IconButton
                compact
                size="xl"
                aria-label={t("home.upload.upload_folder")}
                colorScheme="accent"
                icon={<RiDocumentFolderUploadFill />}
                onClick={() => {
                  folderInput.click()
                }}
              />
              <IconButton
                compact
                size="xl"
                aria-label={t("home.upload.upload_files")}
                icon={<RiDocumentFileUploadFill />}
                onClick={() => {
                  fileInput.click()
                }}
              />
            </HStack>
            <HStack spacing="$4">
              <Checkbox
                checked={asTask()}
                onChange={() => {
                  setAsTask(!asTask())
                }}
              >
                {t("home.upload.add_as_task")}
              </Checkbox>
              <Checkbox
                checked={overwrite()}
                onChange={() => {
                  setOverwrite(!overwrite())
                }}
              >
                {t("home.conflict_policy.overwrite_existing")}
              </Checkbox>
              <Checkbox
                checked={rapid()}
                onChange={() => {
                  setRapid(!rapid())
                }}
              >
                {t("home.upload.try_rapid")}
              </Checkbox>
            </HStack>
          </Show>
        </VStack>
      </Show>
    </VStack>
  )
}

export default Upload
