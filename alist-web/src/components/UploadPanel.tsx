import {
  <PERSON>,
  VStack,
  HStack,
  Text,
  IconButton,
  Progress,
  ProgressIndicator,
  Button,
  Divider,
  Badge,
  Tooltip,
} from "@hope-ui/solid"
import { createSignal, createEffect, Show, For, onMount, onCleanup } from "solid-js"
import { createStore } from "solid-js/store"
import { useT } from "~/hooks"
import { getMainColor } from "~/store"
import { PanelState, UploadStats } from "~/pages/home/<USER>/types"
import {
  RiSystemCloseLine,
  RiMediaPauseFill,
  RiMediaPlayFill,
  RiDeleteBin6Line,
  RiSystemRefreshLine,
} from "solid-icons/ri"
import {
  FiChevronDown,
  FiChevronUp,
} from "solid-icons/fi"
import { getFileSize } from "~/utils"
import { UploadItem } from "~/pages/home/<USER>/UploadItem"
import { uploadManager } from "~/pages/home/<USER>/uploadManager"

interface UploadPanelProps {
  isVisible: boolean
  onClose: () => void
  uploads: any[]
  stats: UploadStats
  onPauseAll: () => void
  onResumeAll: () => void
  onClearCompleted: () => void
  onRetryFailed: () => void
}

export const UploadPanel = (props: UploadPanelProps) => {
  const t = useT()
  
  // 面板状态
  const [panelState, setPanelState] = createStore<PanelState>({
    isMinimized: false,
    isVisible: props.isVisible,
    position: { x: window.innerWidth - 420, y: 100 },
    size: { width: 400, height: 500 }
  })

  // 拖拽状态
  const [isDragging, setIsDragging] = createSignal(false)
  const [dragOffset, setDragOffset] = createSignal({ x: 0, y: 0 })
  const [isResizing, setIsResizing] = createSignal(false)

  let panelRef: HTMLDivElement | undefined
  let headerRef: HTMLDivElement | undefined

  // 监听props变化
  createEffect(() => {
    setPanelState("isVisible", props.isVisible)
  })

  // 拖拽功能
  const handleMouseDown = (e: MouseEvent) => {
    if (!headerRef?.contains(e.target as Node)) return
    
    setIsDragging(true)
    const rect = panelRef!.getBoundingClientRect()
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    })
    e.preventDefault()
  }

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging()) return
    
    const newX = Math.max(0, Math.min(window.innerWidth - panelState.size.width, e.clientX - dragOffset().x))
    const newY = Math.max(0, Math.min(window.innerHeight - panelState.size.height, e.clientY - dragOffset().y))
    
    setPanelState("position", { x: newX, y: newY })
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  // 调整大小功能
  const handleResizeMouseDown = (e: MouseEvent) => {
    setIsResizing(true)
    e.preventDefault()
    e.stopPropagation()
  }

  const handleResizeMouseMove = (e: MouseEvent) => {
    if (!isResizing()) return
    
    const rect = panelRef!.getBoundingClientRect()
    const newWidth = Math.max(300, Math.min(600, e.clientX - rect.left))
    const newHeight = Math.max(200, Math.min(800, e.clientY - rect.top))
    
    setPanelState("size", { width: newWidth, height: newHeight })
  }

  const handleResizeMouseUp = () => {
    setIsResizing(false)
  }

  // 事件监听
  onMount(() => {
    document.addEventListener("mousemove", handleMouseMove)
    document.addEventListener("mouseup", handleMouseUp)
    document.addEventListener("mousemove", handleResizeMouseMove)
    document.addEventListener("mouseup", handleResizeMouseUp)
  })

  onCleanup(() => {
    document.removeEventListener("mousemove", handleMouseMove)
    document.removeEventListener("mouseup", handleMouseUp)
    document.removeEventListener("mousemove", handleResizeMouseMove)
    document.removeEventListener("mouseup", handleResizeMouseUp)
  })

  // 计算总体进度
  const totalProgress = () => {
    if (props.stats.total === 0) return 0
    return Math.round((props.stats.uploadedSize / props.stats.totalSize) * 100)
  }

  // 格式化速度
  const formatSpeed = (speed: number) => {
    return `${getFileSize(speed)}/s`
  }

  return (
    <Show when={panelState.isVisible}>
      <Box
        ref={panelRef}
        position="fixed"
        left={`${panelState.position.x}px`}
        top={`${panelState.position.y}px`}
        width={`${panelState.size.width}px`}
        height={panelState.isMinimized ? "auto" : `${panelState.size.height}px`}
        bg="$loContrast"
        border="1px solid $neutral7"
        borderRadius="$lg"
        boxShadow="$lg"
        zIndex={1000}
        overflow="hidden"
        css={{
          backdropFilter: "blur(10px)",
          backgroundColor: "rgba(255, 255, 255, 0.95)",
          cursor: isDragging() ? "grabbing" : "default",
        }}
      >
        {/* 标题栏 */}
        <HStack
          ref={headerRef}
          p="$3"
          bg="$neutral2"
          borderBottom="1px solid $neutral7"
          justifyContent="space-between"
          alignItems="center"
          cursor="grab"
          onMouseDown={handleMouseDown}
          _active={{ cursor: "grabbing" }}
        >
          <HStack spacing="$2" alignItems="center">
            <Text fontWeight="$semibold" fontSize="$sm">
              {t("home.toolbar.upload")} ({props.stats.total})
            </Text>
            <Show when={props.stats.uploading > 0}>
              <Badge colorScheme="info" size="sm">
                {props.stats.uploading} {t("home.upload.uploading")}
              </Badge>
            </Show>
          </HStack>
          
          <HStack spacing="$1">
            <Tooltip label={panelState.isMinimized ? t("common.expand") : t("common.minimize")}>
              <IconButton
                aria-label="minimize"
                size="sm"
                variant="ghost"
                icon={panelState.isMinimized ? <FiChevronUp /> : <FiChevronDown />}
                onClick={() => setPanelState("isMinimized", !panelState.isMinimized)}
              />
            </Tooltip>
            <Tooltip label={t("common.close")}>
              <IconButton
                aria-label="close"
                size="sm"
                variant="ghost"
                icon={<RiSystemCloseLine />}
                onClick={props.onClose}
              />
            </Tooltip>
          </HStack>
        </HStack>

        <Show when={!panelState.isMinimized}>
          {/* 总体进度 */}
          <Box p="$3" borderBottom="1px solid $neutral7">
            <VStack spacing="$2">
              <HStack justifyContent="space-between" w="$full">
                <Text fontSize="$sm" color="$neutral11">
                  {t("common.progress")}: {props.stats.completed}/{props.stats.total}
                </Text>
                <Text fontSize="$sm" color="$neutral11">
                  {formatSpeed(props.stats.totalSpeed)}
                </Text>
              </HStack>
              <Progress
                w="$full"
                trackColor="$neutral4"
                rounded="$full"
                value={totalProgress()}
                size="sm"
              >
                <ProgressIndicator color={getMainColor()} rounded="$full" />
              </Progress>
              <Text fontSize="$xs" color="$neutral10">
                {getFileSize(props.stats.uploadedSize)} / {getFileSize(props.stats.totalSize)}
              </Text>
            </VStack>
          </Box>

          {/* 操作按钮 */}
          <HStack p="$3" spacing="$2" borderBottom="1px solid $neutral7">
            <Button
              size="sm"
              variant="subtle"
              leftIcon={<RiMediaPauseFill />}
              onClick={props.onPauseAll}
              disabled={props.stats.uploading === 0}
            >
              {t("common.pause_all")}
            </Button>
            <Button
              size="sm"
              variant="subtle"
              leftIcon={<RiMediaPlayFill />}
              onClick={props.onResumeAll}
              disabled={props.stats.paused === 0}
            >
              {t("common.resume_all")}
            </Button>
            <Button
              size="sm"
              variant="subtle"
              leftIcon={<RiSystemRefreshLine />}
              onClick={props.onRetryFailed}
              disabled={props.stats.failed === 0}
            >
              {t("common.retry")}
            </Button>
            <Button
              size="sm"
              variant="subtle"
              leftIcon={<RiDeleteBin6Line />}
              onClick={props.onClearCompleted}
              disabled={props.stats.completed === 0}
            >
              {t("home.upload.clear_done")}
            </Button>
          </HStack>

          {/* 上传列表 */}
          <Box
            flex="1"
            overflow="auto"
            maxHeight={`${panelState.size.height - 200}px`}
          >
            <VStack spacing="$2" p="$3">
              <For each={props.uploads}>
                {(upload) => (
                  <UploadItem
                    {...upload}
                    onPause={() => uploadManager.pauseUpload(upload.id)}
                    onResume={() => uploadManager.resumeUpload(upload.id)}
                    onCancel={() => uploadManager.cancelUpload(upload.id)}
                    onRetry={() => uploadManager.retryUpload(upload.id)}
                  />
                )}
              </For>
              <Show when={props.uploads.length === 0}>
                <Text color="$neutral10" textAlign="center" py="$8">
                  {t("common.no_data")}
                </Text>
              </Show>
            </VStack>
          </Box>

          {/* 调整大小手柄 */}
          <Box
            position="absolute"
            bottom="0"
            right="0"
            width="$4"
            height="$4"
            cursor="se-resize"
            onMouseDown={handleResizeMouseDown}
            css={{
              background: `linear-gradient(-45deg, transparent 30%, ${getMainColor()} 30%, ${getMainColor()} 40%, transparent 40%, transparent 60%, ${getMainColor()} 60%, ${getMainColor()} 70%, transparent 70%)`,
            }}
          />
        </Show>
      </Box>
    </Show>
  )
}
