package main

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
)

func main() {
	fmt.Println("🔍 API可达性测试...")
	
	baseURL := "http://localhost:5244"
	
	// 测试1: 检查流式上传配置API
	fmt.Println("\n⚙️  测试1: 检查流式上传配置API...")
	err := testAPI("GET", baseURL+"/api/streaming/config", nil)
	if err != nil {
		fmt.Printf("❌ 配置API失败: %v\n", err)
	} else {
		fmt.Printf("✅ 配置API正常\n")
	}
	
	// 测试2: 检查活跃上传API
	fmt.Println("\n📋 测试2: 检查活跃上传API...")
	err = testAPI("GET", baseURL+"/api/streaming/uploads", nil)
	if err != nil {
		fmt.Printf("❌ 活跃上传API失败: %v\n", err)
	} else {
		fmt.Printf("✅ 活跃上传API正常\n")
	}
	
	// 测试3: 检查测试API
	fmt.Println("\n🧪 测试3: 检查测试API...")
	err = testAPI("GET", baseURL+"/api/streaming/test", nil)
	if err != nil {
		fmt.Printf("❌ 测试API失败: %v\n", err)
	} else {
		fmt.Printf("✅ 测试API正常\n")
	}
	
	// 测试4: 尝试调用流式上传API（应该失败，因为没有session_id）
	fmt.Println("\n🚀 测试4: 尝试调用流式上传API...")
	testData := []byte("test data")
	err = testAPI("POST", baseURL+"/api/streaming/upload/test-session", testData)
	if err != nil {
		fmt.Printf("✅ 流式上传API可达（预期错误）: %v\n", err)
	} else {
		fmt.Printf("⚠️  流式上传API意外成功\n")
	}
	
	fmt.Println("\n🎯 API可达性测试完成！")
}

func testAPI(method, url string, data []byte) error {
	var body io.Reader
	if data != nil {
		body = bytes.NewBuffer(data)
	}
	
	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return err
	}
	
	if data != nil {
		req.Header.Set("Content-Type", "application/octet-stream")
	}
	
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	
	fmt.Printf("   响应状态: %d\n", resp.StatusCode)
	fmt.Printf("   响应内容: %s\n", string(responseBody))
	
	return nil
}
