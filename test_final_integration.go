package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

func main() {
	fmt.Println("🎯 最终集成测试 - 流式上传与断点续传完整集成")
	
	baseURL := "http://localhost:5244"
	userID := "final_test_user"
	
	// 步骤1: 创建上传会话
	fmt.Println("\n📝 步骤1: 创建上传会话...")
	sessionID, err := createSession(baseURL, userID)
	if err != nil {
		fmt.Printf("❌ 创建会话失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 会话创建成功: %s\n", sessionID)
	
	// 步骤2: 检查初始状态
	fmt.Println("\n📊 步骤2: 检查初始状态...")
	progress, err := getProgress(baseURL, sessionID)
	if err != nil {
		fmt.Printf("❌ 获取初始状态失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 初始状态: %.2f%% (%d/%d chunks)\n", 
		progress["progress_percent"].(float64), 
		int(progress["completed_chunks"].(float64)), 
		int(progress["total_chunks"].(float64)))
	
	// 步骤3: 启动流式上传
	fmt.Println("\n🚀 步骤3: 启动流式上传...")
	testData := generateTestData(256) // 256字节，应该产生2个128字节的分片
	
	go func() {
		err := startStreamingUpload(baseURL, sessionID, testData)
		if err != nil {
			fmt.Printf("❌ 流式上传失败: %v\n", err)
		} else {
			fmt.Printf("✅ 流式上传完成\n")
		}
	}()
	
	// 步骤4: 实时监控进度
	fmt.Println("\n📊 步骤4: 实时监控进度...")
	finalProgress, err := monitorProgress(baseURL, sessionID, 15*time.Second)
	if err != nil {
		fmt.Printf("❌ 监控失败: %v\n", err)
		return
	}
	
	// 步骤5: 验证最终结果
	fmt.Println("\n✅ 步骤5: 验证最终结果...")
	fmt.Printf("📊 最终进度: %.2f%%\n", finalProgress["progress_percent"].(float64))
	fmt.Printf("📊 完成分片: %d/%d\n", 
		int(finalProgress["completed_chunks"].(float64)), 
		int(finalProgress["total_chunks"].(float64)))
	fmt.Printf("📊 上传字节: %d/%d\n", 
		int(finalProgress["uploaded_bytes"].(float64)), 
		int(finalProgress["file_size"].(float64)))
	fmt.Printf("📊 状态: %s\n", finalProgress["status"].(string))
	
	if finalProgress["progress_percent"].(float64) == 100.0 {
		fmt.Println("\n🎉 集成测试完全成功！流式上传与断点续传完美集成！")
	} else {
		fmt.Printf("\n⚠️  集成测试部分成功，进度: %.2f%%\n", finalProgress["progress_percent"].(float64))
	}
}

func createSession(baseURL, userID string) (string, error) {
	reqData := map[string]interface{}{
		"file_name":  "final_test.txt",
		"file_path":  "/final/final_test.txt",
		"file_size":  256,
		"chunk_size": 128,
		"metadata": map[string]string{
			"test_type": "final_integration",
		},
	}
	
	jsonData, _ := json.Marshal(reqData)
	
	req, err := http.NewRequest("POST", baseURL+"/api/resumable/upload", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", err
	}
	
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-User-ID", userID)
	
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	
	var apiResp map[string]interface{}
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return "", err
	}
	
	if code, ok := apiResp["code"].(float64); !ok || code != 200 {
		return "", fmt.Errorf("API error: %v", apiResp["message"])
	}
	
	data := apiResp["data"].(map[string]interface{})
	return data["session_id"].(string), nil
}

func getProgress(baseURL, sessionID string) (map[string]interface{}, error) {
	url := fmt.Sprintf("%s/api/resumable/upload/%s/progress", baseURL, sessionID)
	
	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	
	var apiResp map[string]interface{}
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, err
	}
	
	if code, ok := apiResp["code"].(float64); !ok || code != 200 {
		return nil, fmt.Errorf("API error: %v", apiResp["message"])
	}
	
	return apiResp["data"].(map[string]interface{}), nil
}

func startStreamingUpload(baseURL, sessionID string, data []byte) error {
	// 使用与会话匹配的分片大小
	url := fmt.Sprintf("%s/api/streaming/upload/%s?chunk_size=128&max_concurrent=1&enable_retry=true", 
		baseURL, sessionID)
	
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(data))
	if err != nil {
		return err
	}
	
	req.Header.Set("Content-Type", "application/octet-stream")
	
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("upload failed: %s", string(body))
	}
	
	return nil
}

func monitorProgress(baseURL, sessionID string, timeout time.Duration) (map[string]interface{}, error) {
	startTime := time.Now()
	lastProgress := float64(-1)
	
	fmt.Printf("⏱️  开始监控进度（超时: %v）...\n", timeout)
	
	for time.Since(startTime) < timeout {
		progress, err := getProgress(baseURL, sessionID)
		if err != nil {
			fmt.Printf("⚠️  获取进度失败: %v\n", err)
			time.Sleep(1 * time.Second)
			continue
		}
		
		currentProgress := progress["progress_percent"].(float64)
		
		// 只在进度变化时打印
		if currentProgress != lastProgress {
			fmt.Printf("📊 进度更新: %.2f%% (%d/%d chunks), 状态: %s\n", 
				currentProgress,
				int(progress["completed_chunks"].(float64)),
				int(progress["total_chunks"].(float64)),
				progress["status"].(string))
			lastProgress = currentProgress
		}
		
		// 检查是否完成
		if progress["status"].(string) == "completed" {
			fmt.Printf("✅ 上传完成！最终进度: %.2f%%\n", currentProgress)
			return progress, nil
		}
		
		if progress["status"].(string) == "failed" {
			return progress, fmt.Errorf("upload failed")
		}
		
		time.Sleep(500 * time.Millisecond)
	}
	
	// 超时后获取最终状态
	finalProgress, err := getProgress(baseURL, sessionID)
	if err == nil {
		fmt.Printf("⏰ 监控超时，最终进度: %.2f%%\n", finalProgress["progress_percent"].(float64))
		return finalProgress, nil
	}
	
	return nil, fmt.Errorf("monitoring timeout and failed to get final progress: %v", err)
}

func generateTestData(size int) []byte {
	data := make([]byte, size)
	// 生成有模式的测试数据
	for i := range data {
		data[i] = byte((i % 26) + 'A') // A-Z循环
	}
	return data
}
