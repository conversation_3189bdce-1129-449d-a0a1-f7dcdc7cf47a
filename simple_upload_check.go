package main

import (
	"bytes"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"time"
)

func main() {
	fmt.Println("🧪 简单上传测试...")
	
	baseURL := "http://localhost:5244"
	
	// 测试1: 检查服务器状态
	fmt.Println("📡 检查服务器状态...")
	resp, err := http.Get(baseURL + "/ping")
	if err != nil {
		fmt.Printf("❌ 服务器连接失败: %v\n", err)
		return
	}
	resp.Body.Close()
	fmt.Printf("✅ 服务器状态: %d\n", resp.StatusCode)
	
	// 测试2: 尝试访问上传指标API（无认证）
	fmt.Println("📊 测试上传指标API...")
	resp, err = http.Get(baseURL + "/api/admin/upload/metrics")
	if err != nil {
		fmt.Printf("❌ 指标API请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	body, _ := io.ReadAll(resp.Body)
	fmt.Printf("📊 指标API响应 (%d): %s\n", resp.StatusCode, string(body))
	
	// 测试3: 尝试上传文件（无认证）
	fmt.Println("📤 测试文件上传...")
	uploadResp := testUpload(baseURL, "test_file.txt", "test content")
	fmt.Printf("📤 上传响应: %s\n", uploadResp)
	
	fmt.Println("🎉 测试完成！")
}

func testUpload(baseURL, filename, content string) string {
	// 创建multipart form
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)
	
	part, err := writer.CreateFormFile("file", filename)
	if err != nil {
		return fmt.Sprintf("创建表单失败: %v", err)
	}
	
	_, err = part.Write([]byte(content))
	if err != nil {
		return fmt.Sprintf("写入内容失败: %v", err)
	}
	
	writer.Close()
	
	// 创建请求
	req, err := http.NewRequest("PUT", baseURL+"/api/fs/form", &buf)
	if err != nil {
		return fmt.Sprintf("创建请求失败: %v", err)
	}
	
	// 设置headers
	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("File-Path", "/"+filename)
	
	// 发送请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Sprintf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()
	
	body, _ := io.ReadAll(resp.Body)
	return fmt.Sprintf("状态码: %d, 响应: %s", resp.StatusCode, string(body))
}
