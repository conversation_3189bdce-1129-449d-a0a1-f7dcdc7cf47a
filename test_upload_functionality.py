#!/usr/bin/env python3
"""
AList上传功能全面测试脚本
测试上传功能的各个方面：API、容量控制、并发限制、错误处理等
"""

import requests
import json
import time
import os
import tempfile
import threading
from concurrent.futures import ThreadPoolExecutor
import hashlib

BASE_URL = "http://localhost:5244"

class UploadTester:
    def __init__(self):
        self.session = requests.Session()
        self.test_results = []
        
    def log_test(self, test_name, success, message="", details=None):
        """记录测试结果"""
        result = {
            "test": test_name,
            "success": success,
            "message": message,
            "details": details,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        self.test_results.append(result)
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        if details:
            print(f"   Details: {details}")
    
    def test_upload_metrics_api(self):
        """测试上传指标API"""
        try:
            response = self.session.get(f"{BASE_URL}/api/upload/metrics")
            if response.status_code == 200:
                data = response.json()
                if "data" in data and "status" in data["data"]:
                    self.log_test("上传指标API", True, "API响应正常", data["data"])
                else:
                    self.log_test("上传指标API", False, "响应格式错误", data)
            else:
                self.log_test("上传指标API", False, f"HTTP错误: {response.status_code}")
        except Exception as e:
            self.log_test("上传指标API", False, f"请求异常: {str(e)}")
    
    def test_buffer_pool_api(self):
        """测试缓冲区池API"""
        try:
            response = self.session.get(f"{BASE_URL}/api/buffer-pool/status")
            if response.status_code == 200:
                data = response.json()
                if "data" in data and "status" in data["data"]:
                    self.log_test("缓冲区池API", True, "API响应正常", data["data"])
                else:
                    self.log_test("缓冲区池API", False, "响应格式错误", data)
            else:
                self.log_test("缓冲区池API", False, f"HTTP错误: {response.status_code}")
        except Exception as e:
            self.log_test("缓冲区池API", False, f"请求异常: {str(e)}")
    
    def test_streaming_config_api(self):
        """测试流式上传配置API"""
        try:
            response = self.session.get(f"{BASE_URL}/api/streaming/config")
            if response.status_code == 200:
                data = response.json()
                if "data" in data and "default_config" in data["data"]:
                    config = data["data"]["default_config"]
                    expected_keys = ["chunk_size", "max_concurrent", "enable_retry", "max_retries", "timeout"]
                    if all(key in config for key in expected_keys):
                        self.log_test("流式上传配置API", True, "配置完整", config)
                    else:
                        self.log_test("流式上传配置API", False, "配置不完整", config)
                else:
                    self.log_test("流式上传配置API", False, "响应格式错误", data)
            else:
                self.log_test("流式上传配置API", False, f"HTTP错误: {response.status_code}")
        except Exception as e:
            self.log_test("流式上传配置API", False, f"请求异常: {str(e)}")
    
    def test_resumable_upload_session(self):
        """测试断点续传会话创建"""
        try:
            payload = {
                "file_name": "test_resumable.txt",
                "file_path": "/test_resumable.txt",
                "file_size": 1024,
                "chunk_size": 256
            }
            headers = {
                "Content-Type": "application/json",
                "X-User-ID": "test_user"
            }
            
            response = self.session.post(
                f"{BASE_URL}/api/resumable/upload",
                json=payload,
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                if "data" in data and "session_id" in data["data"]:
                    session_data = data["data"]
                    expected_keys = ["session_id", "file_name", "file_size", "chunk_size", "total_chunks"]
                    if all(key in session_data for key in expected_keys):
                        self.log_test("断点续传会话创建", True, "会话创建成功", session_data)
                        return session_data["session_id"]
                    else:
                        self.log_test("断点续传会话创建", False, "会话数据不完整", session_data)
                else:
                    self.log_test("断点续传会话创建", False, "响应格式错误", data)
            else:
                self.log_test("断点续传会话创建", False, f"HTTP错误: {response.status_code}")
        except Exception as e:
            self.log_test("断点续传会话创建", False, f"请求异常: {str(e)}")
        return None
    
    def test_concurrent_upload_limits(self):
        """测试并发上传限制"""
        def create_upload_session(user_id):
            try:
                payload = {
                    "file_name": f"concurrent_test_{user_id}.txt",
                    "file_path": f"/concurrent_test_{user_id}.txt",
                    "file_size": 512,
                    "chunk_size": 256
                }
                headers = {
                    "Content-Type": "application/json",
                    "X-User-ID": f"user_{user_id}"
                }
                
                response = self.session.post(
                    f"{BASE_URL}/api/resumable/upload",
                    json=payload,
                    headers=headers
                )
                return response.status_code == 200
            except:
                return False
        
        # 测试多个并发会话
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(create_upload_session, i) for i in range(10)]
            results = [future.result() for future in futures]
        
        success_count = sum(results)
        if success_count > 0:
            self.log_test("并发上传限制", True, f"成功创建 {success_count}/10 个并发会话")
        else:
            self.log_test("并发上传限制", False, "无法创建任何并发会话")
    
    def test_file_size_validation(self):
        """测试文件大小验证"""
        test_cases = [
            {"file_size": 0, "should_fail": True, "desc": "零字节文件"},
            {"file_size": -1, "should_fail": True, "desc": "负数文件大小"},
            {"file_size": 1024, "should_fail": False, "desc": "正常文件大小"},
            {"file_size": 1024*1024*1024, "should_fail": False, "desc": "大文件"},
        ]
        
        for case in test_cases:
            try:
                payload = {
                    "file_name": f"size_test_{case['file_size']}.txt",
                    "file_path": f"/size_test_{case['file_size']}.txt",
                    "file_size": case["file_size"],
                    "chunk_size": 256
                }
                headers = {
                    "Content-Type": "application/json",
                    "X-User-ID": "test_user"
                }
                
                response = self.session.post(
                    f"{BASE_URL}/api/resumable/upload",
                    json=payload,
                    headers=headers
                )
                
                success = response.status_code == 200
                expected_success = not case["should_fail"]
                
                if success == expected_success:
                    self.log_test(f"文件大小验证-{case['desc']}", True, "验证正确")
                else:
                    self.log_test(f"文件大小验证-{case['desc']}", False, 
                                f"期望{'失败' if case['should_fail'] else '成功'}，实际{'成功' if success else '失败'}")
            except Exception as e:
                self.log_test(f"文件大小验证-{case['desc']}", False, f"请求异常: {str(e)}")
    
    def test_chunk_size_validation(self):
        """测试分片大小验证"""
        test_cases = [
            {"chunk_size": 0, "should_use_default": True, "desc": "零分片大小"},
            {"chunk_size": 512, "should_use_default": False, "desc": "小分片大小"},
            {"chunk_size": 4*1024*1024, "should_use_default": False, "desc": "标准分片大小"},
            {"chunk_size": 16*1024*1024, "should_use_default": False, "desc": "大分片大小"},
        ]
        
        for case in test_cases:
            try:
                payload = {
                    "file_name": f"chunk_test_{case['chunk_size']}.txt",
                    "file_path": f"/chunk_test_{case['chunk_size']}.txt",
                    "file_size": 1024*1024,  # 1MB
                    "chunk_size": case["chunk_size"]
                }
                headers = {
                    "Content-Type": "application/json",
                    "X-User-ID": "test_user"
                }
                
                response = self.session.post(
                    f"{BASE_URL}/api/resumable/upload",
                    json=payload,
                    headers=headers
                )
                
                if response.status_code == 200:
                    data = response.json()
                    returned_chunk_size = data["data"]["chunk_size"]
                    
                    if case["should_use_default"]:
                        # 应该使用默认值 4MB
                        if returned_chunk_size == 4*1024*1024:
                            self.log_test(f"分片大小验证-{case['desc']}", True, "使用默认分片大小")
                        else:
                            self.log_test(f"分片大小验证-{case['desc']}", False, 
                                        f"期望默认值4MB，实际{returned_chunk_size}")
                    else:
                        # 应该使用指定值
                        if returned_chunk_size == case["chunk_size"]:
                            self.log_test(f"分片大小验证-{case['desc']}", True, "使用指定分片大小")
                        else:
                            self.log_test(f"分片大小验证-{case['desc']}", False, 
                                        f"期望{case['chunk_size']}，实际{returned_chunk_size}")
                else:
                    self.log_test(f"分片大小验证-{case['desc']}", False, f"HTTP错误: {response.status_code}")
            except Exception as e:
                self.log_test(f"分片大小验证-{case['desc']}", False, f"请求异常: {str(e)}")
    
    def test_upload_path_validation(self):
        """测试上传路径验证"""
        test_cases = [
            {"path": "/normal/path/file.txt", "should_succeed": True, "desc": "正常路径"},
            {"path": "/path/../file.txt", "should_succeed": False, "desc": "相对路径攻击"},
            {"path": "//double/slash.txt", "should_succeed": True, "desc": "双斜杠路径"},
            {"path": "/path/with spaces/file.txt", "should_succeed": True, "desc": "包含空格的路径"},
            {"path": "/中文路径/文件.txt", "should_succeed": True, "desc": "中文路径"},
        ]
        
        for case in test_cases:
            try:
                payload = {
                    "file_name": "path_test.txt",
                    "file_path": case["path"],
                    "file_size": 1024,
                    "chunk_size": 256
                }
                headers = {
                    "Content-Type": "application/json",
                    "X-User-ID": "test_user"
                }
                
                response = self.session.post(
                    f"{BASE_URL}/api/resumable/upload",
                    json=payload,
                    headers=headers
                )
                
                success = response.status_code == 200
                
                if success == case["should_succeed"]:
                    self.log_test(f"路径验证-{case['desc']}", True, "验证正确")
                else:
                    self.log_test(f"路径验证-{case['desc']}", False, 
                                f"期望{'成功' if case['should_succeed'] else '失败'}，实际{'成功' if success else '失败'}")
            except Exception as e:
                self.log_test(f"路径验证-{case['desc']}", False, f"请求异常: {str(e)}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始AList上传功能全面测试")
        print("=" * 60)
        
        # 基础API测试
        print("\n📊 基础API测试")
        self.test_upload_metrics_api()
        self.test_buffer_pool_api()
        self.test_streaming_config_api()
        
        # 断点续传测试
        print("\n🔄 断点续传测试")
        self.test_resumable_upload_session()
        
        # 并发控制测试
        print("\n⚡ 并发控制测试")
        self.test_concurrent_upload_limits()
        
        # 参数验证测试
        print("\n🔍 参数验证测试")
        self.test_file_size_validation()
        self.test_chunk_size_validation()
        self.test_upload_path_validation()
        
        # 生成测试报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📋 测试报告")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests} ✅")
        print(f"失败: {failed_tests} ❌")
        print(f"通过率: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test']}: {result['message']}")
        
        # 保存详细报告到文件
        report_file = f"upload_test_report_{int(time.time())}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        print(f"\n📄 详细报告已保存到: {report_file}")

if __name__ == "__main__":
    tester = UploadTester()
    tester.run_all_tests()
