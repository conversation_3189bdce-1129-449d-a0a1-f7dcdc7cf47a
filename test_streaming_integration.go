package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

type UploadSession struct {
	SessionID   string `json:"session_id"`
	FileName    string `json:"file_name"`
	FileSize    int64  `json:"file_size"`
	ChunkSize   int64  `json:"chunk_size"`
	TotalChunks int64  `json:"total_chunks"`
}

type UploadProgress struct {
	SessionID       string  `json:"session_id"`
	FileName        string  `json:"file_name"`
	FileSize        int64   `json:"file_size"`
	UploadedBytes   int64   `json:"uploaded_bytes"`
	TotalChunks     int64   `json:"total_chunks"`
	CompletedChunks int64   `json:"completed_chunks"`
	ProgressPercent float64 `json:"progress_percent"`
	Status          string  `json:"status"`
}

func main() {
	fmt.Println("🧪 流式上传与断点续传集成测试...")
	
	baseURL := "http://localhost:5244"
	userID := "test_user"
	
	// 测试1: 创建上传会话
	fmt.Println("\n📝 测试1: 创建上传会话...")
	session, err := createUploadSession(baseURL, userID)
	if err != nil {
		fmt.Printf("❌ 创建上传会话失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 上传会话创建成功: %s\n", session.SessionID)
	fmt.Printf("   文件大小: %d bytes, 分片大小: %d bytes, 总分片数: %d\n", 
		session.FileSize, session.ChunkSize, session.TotalChunks)
	
	// 测试2: 检查初始进度
	fmt.Println("\n📊 测试2: 检查初始进度...")
	progress, err := getUploadProgress(baseURL, session.SessionID)
	if err != nil {
		fmt.Printf("❌ 获取初始进度失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 初始进度: %.2f%% (%d/%d chunks)\n", 
		progress.ProgressPercent, progress.CompletedChunks, progress.TotalChunks)
	
	// 测试3: 开始流式上传
	fmt.Println("\n🚀 测试3: 开始流式上传...")
	testData := generateTestData(int(session.FileSize))
	
	// 启动流式上传
	go func() {
		err := startStreamingUpload(baseURL, session.SessionID, testData)
		if err != nil {
			fmt.Printf("❌ 流式上传失败: %v\n", err)
		}
	}()
	
	fmt.Printf("✅ 流式上传已启动\n")
	
	// 测试4: 实时监控进度
	fmt.Println("\n📊 测试4: 实时监控进度...")
	err = monitorProgressWithDetails(baseURL, session.SessionID, 30*time.Second)
	if err != nil {
		fmt.Printf("❌ 监控进度失败: %v\n", err)
		return
	}
	
	// 测试5: 验证最终状态
	fmt.Println("\n✅ 测试5: 验证最终状态...")
	finalProgress, err := getUploadProgress(baseURL, session.SessionID)
	if err != nil {
		fmt.Printf("❌ 获取最终进度失败: %v\n", err)
		return
	}
	
	fmt.Printf("📊 最终状态:\n")
	fmt.Printf("   进度: %.2f%% (%d/%d chunks)\n", 
		finalProgress.ProgressPercent, finalProgress.CompletedChunks, finalProgress.TotalChunks)
	fmt.Printf("   上传字节: %d/%d bytes\n", 
		finalProgress.UploadedBytes, finalProgress.FileSize)
	fmt.Printf("   状态: %s\n", finalProgress.Status)
	
	if finalProgress.ProgressPercent == 100.0 && finalProgress.Status == "completed" {
		fmt.Println("\n🎉 集成测试完全成功！流式上传与断点续传系统完美集成！")
	} else {
		fmt.Printf("\n⚠️  集成测试部分成功，最终进度: %.2f%%\n", finalProgress.ProgressPercent)
	}
}

func createUploadSession(baseURL, userID string) (*UploadSession, error) {
	reqData := map[string]interface{}{
		"file_name":  "integration_test.txt",
		"file_path":  "/uploads/integration_test.txt",
		"file_size":  512 * 1024, // 512KB，较小的文件便于测试
		"chunk_size": 128 * 1024,  // 128KB分片
		"metadata": map[string]string{
			"content_type": "text/plain",
			"test_type":    "integration",
		},
	}
	
	jsonData, _ := json.Marshal(reqData)
	
	req, err := http.NewRequest("POST", baseURL+"/api/resumable/upload", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, err
	}
	
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-User-ID", userID)
	
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, err
	}
	
	if apiResp.Code != 200 {
		return nil, fmt.Errorf("API error: %s", apiResp.Message)
	}
	
	sessionData, _ := json.Marshal(apiResp.Data)
	var session UploadSession
	if err := json.Unmarshal(sessionData, &session); err != nil {
		return nil, err
	}
	
	return &session, nil
}

func getUploadProgress(baseURL, sessionID string) (*UploadProgress, error) {
	url := fmt.Sprintf("%s/api/resumable/upload/%s/progress", baseURL, sessionID)
	
	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, err
	}
	
	if apiResp.Code != 200 {
		return nil, fmt.Errorf("API error: %s", apiResp.Message)
	}
	
	progressData, _ := json.Marshal(apiResp.Data)
	var progress UploadProgress
	if err := json.Unmarshal(progressData, &progress); err != nil {
		return nil, err
	}
	
	return &progress, nil
}

func startStreamingUpload(baseURL, sessionID string, data []byte) error {
	// 使用较小的分片大小和较低的并发度，便于观察进度
	url := fmt.Sprintf("%s/api/streaming/upload/%s?chunk_size=131072&max_concurrent=1", baseURL, sessionID)
	
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(data))
	if err != nil {
		return err
	}
	
	req.Header.Set("Content-Type", "application/octet-stream")
	
	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("upload failed: %s", string(body))
	}
	
	return nil
}

func monitorProgressWithDetails(baseURL, sessionID string, timeout time.Duration) error {
	startTime := time.Now()
	lastProgress := float64(-1)
	
	fmt.Printf("⏱️  开始监控进度（超时: %v）...\n", timeout)
	
	for time.Since(startTime) < timeout {
		progress, err := getUploadProgress(baseURL, sessionID)
		if err != nil {
			fmt.Printf("⚠️  获取进度失败: %v\n", err)
			time.Sleep(1 * time.Second)
			continue
		}
		
		// 只在进度变化时打印
		if progress.ProgressPercent != lastProgress {
			fmt.Printf("📊 进度更新: %.2f%% (%d/%d chunks), 状态: %s, 上传: %d/%d bytes\n", 
				progress.ProgressPercent, progress.CompletedChunks, progress.TotalChunks,
				progress.Status, progress.UploadedBytes, progress.FileSize)
			lastProgress = progress.ProgressPercent
		}
		
		// 检查是否完成
		if progress.Status == "completed" {
			fmt.Printf("✅ 上传完成！最终进度: %.2f%%\n", progress.ProgressPercent)
			return nil
		}
		
		if progress.Status == "failed" {
			return fmt.Errorf("upload failed")
		}
		
		time.Sleep(500 * time.Millisecond)
	}
	
	// 超时后获取最终状态
	finalProgress, err := getUploadProgress(baseURL, sessionID)
	if err == nil {
		fmt.Printf("⏰ 监控超时，最终进度: %.2f%% (%d/%d chunks)\n", 
			finalProgress.ProgressPercent, finalProgress.CompletedChunks, finalProgress.TotalChunks)
	}
	
	return nil
}

func generateTestData(size int) []byte {
	data := make([]byte, size)
	// 生成有模式的测试数据，便于验证
	for i := range data {
		data[i] = byte((i % 256))
	}
	return data
}
