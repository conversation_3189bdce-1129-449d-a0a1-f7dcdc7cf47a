package concurrency

import (
	"context"
	"fmt"
	"sync"
	"time"

	"golang.org/x/sync/semaphore"
	log "github.com/sirupsen/logrus"
)

// UserConcurrencyManager 用户级并发控制管理器
type UserConcurrencyManager struct {
	userLimits    map[string]*semaphore.Weighted
	globalLimit   *semaphore.Weighted
	maxPerUser    int64
	maxGlobal     int64
	mu            sync.RWMutex
	metrics       *ConcurrencyMetrics
}

// ConcurrencyMetrics 并发控制指标
type ConcurrencyMetrics struct {
	ActiveUploads     map[string]int64 // 每用户活跃上传数
	TotalActiveUploads int64           // 全局活跃上传数
	QueuedUploads     int64            // 排队上传数
	RejectedUploads   int64            // 被拒绝的上传数
	mu                sync.RWMutex
}

// NewUserConcurrencyManager 创建用户并发管理器
func NewUserConcurrencyManager(maxPerUser, maxGlobal int64) *UserConcurrencyManager {
	return &UserConcurrencyManager{
		userLimits:  make(map[string]*semaphore.Weighted),
		globalLimit: semaphore.NewWeighted(maxGlobal),
		maxPerUser:  maxPerUser,
		maxGlobal:   maxGlobal,
		metrics: &ConcurrencyMetrics{
			ActiveUploads: make(map[string]int64),
		},
	}
}

// AcquireUpload 获取上传资源
func (ucm *UserConcurrencyManager) AcquireUpload(userID string, ctx context.Context) error {
	startTime := time.Now()
	
	// 获取或创建用户信号量
	ucm.mu.RLock()
	userSem, exists := ucm.userLimits[userID]
	ucm.mu.RUnlock()
	
	if !exists {
		ucm.mu.Lock()
		// 双重检查
		if userSem, exists = ucm.userLimits[userID]; !exists {
			userSem = semaphore.NewWeighted(ucm.maxPerUser)
			ucm.userLimits[userID] = userSem
		}
		ucm.mu.Unlock()
	}
	
	// 先尝试获取全局资源
	if err := ucm.globalLimit.Acquire(ctx, 1); err != nil {
		ucm.updateMetrics(userID, "rejected")
		log.Warnf("Global upload limit reached for user %s: %v", userID, err)
		return fmt.Errorf("server upload limit reached, please try again later")
	}
	
	// 再获取用户资源
	if err := userSem.Acquire(ctx, 1); err != nil {
		// 释放已获取的全局资源
		ucm.globalLimit.Release(1)
		ucm.updateMetrics(userID, "rejected")
		log.Warnf("User upload limit reached for user %s: %v", userID, err)
		return fmt.Errorf("too many concurrent uploads, please wait for current uploads to complete")
	}
	
	// 更新指标
	ucm.updateMetrics(userID, "acquired")
	
	duration := time.Since(startTime)
	log.Debugf("Upload resource acquired for user %s in %v", userID, duration)
	
	return nil
}

// ReleaseUpload 释放上传资源
func (ucm *UserConcurrencyManager) ReleaseUpload(userID string) {
	ucm.mu.RLock()
	userSem, exists := ucm.userLimits[userID]
	ucm.mu.RUnlock()
	
	if exists {
		userSem.Release(1)
	}
	
	ucm.globalLimit.Release(1)
	ucm.updateMetrics(userID, "released")
	
	log.Debugf("Upload resource released for user %s", userID)
}

// updateMetrics 更新并发指标
func (ucm *UserConcurrencyManager) updateMetrics(userID, action string) {
	ucm.metrics.mu.Lock()
	defer ucm.metrics.mu.Unlock()
	
	switch action {
	case "acquired":
		ucm.metrics.ActiveUploads[userID]++
		ucm.metrics.TotalActiveUploads++
	case "released":
		if ucm.metrics.ActiveUploads[userID] > 0 {
			ucm.metrics.ActiveUploads[userID]--
			ucm.metrics.TotalActiveUploads--
		}
		// 清理空的用户记录
		if ucm.metrics.ActiveUploads[userID] == 0 {
			delete(ucm.metrics.ActiveUploads, userID)
		}
	case "rejected":
		ucm.metrics.RejectedUploads++
	}
}

// GetMetrics 获取并发指标
func (ucm *UserConcurrencyManager) GetMetrics() *ConcurrencyMetrics {
	ucm.metrics.mu.RLock()
	defer ucm.metrics.mu.RUnlock()
	
	// 创建副本避免并发访问问题
	metrics := &ConcurrencyMetrics{
		ActiveUploads:      make(map[string]int64),
		TotalActiveUploads: ucm.metrics.TotalActiveUploads,
		QueuedUploads:      ucm.metrics.QueuedUploads,
		RejectedUploads:    ucm.metrics.RejectedUploads,
	}
	
	for userID, count := range ucm.metrics.ActiveUploads {
		metrics.ActiveUploads[userID] = count
	}
	
	return metrics
}

// GetUserActiveUploads 获取用户活跃上传数
func (ucm *UserConcurrencyManager) GetUserActiveUploads(userID string) int64 {
	ucm.metrics.mu.RLock()
	defer ucm.metrics.mu.RUnlock()
	
	return ucm.metrics.ActiveUploads[userID]
}

// GetGlobalActiveUploads 获取全局活跃上传数
func (ucm *UserConcurrencyManager) GetGlobalActiveUploads() int64 {
	ucm.metrics.mu.RLock()
	defer ucm.metrics.mu.RUnlock()
	
	return ucm.metrics.TotalActiveUploads
}

// CleanupIdleUsers 清理空闲用户的信号量（定期调用）
func (ucm *UserConcurrencyManager) CleanupIdleUsers() {
	ucm.mu.Lock()
	defer ucm.mu.Unlock()
	
	ucm.metrics.mu.RLock()
	activeUsers := make(map[string]bool)
	for userID := range ucm.metrics.ActiveUploads {
		activeUsers[userID] = true
	}
	ucm.metrics.mu.RUnlock()
	
	// 删除没有活跃上传的用户信号量
	for userID := range ucm.userLimits {
		if !activeUsers[userID] {
			delete(ucm.userLimits, userID)
			log.Debugf("Cleaned up idle user semaphore for user %s", userID)
		}
	}
}

// 全局并发管理器实例
var GlobalConcurrencyManager *UserConcurrencyManager

// InitConcurrencyManager 初始化全局并发管理器
func InitConcurrencyManager(maxPerUser, maxGlobal int64) {
	GlobalConcurrencyManager = NewUserConcurrencyManager(maxPerUser, maxGlobal)
	
	// 启动定期清理协程
	go func() {
		ticker := time.NewTicker(5 * time.Minute)
		defer ticker.Stop()
		
		for range ticker.C {
			GlobalConcurrencyManager.CleanupIdleUsers()
		}
	}()
	
	log.Infof("Concurrency manager initialized: maxPerUser=%d, maxGlobal=%d", maxPerUser, maxGlobal)
}
