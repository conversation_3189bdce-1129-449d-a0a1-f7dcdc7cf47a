package concurrency

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"
)

func TestNewUserConcurrencyManager(t *testing.T) {
	maxPerUser := int64(3)
	maxGlobal := int64(10)
	
	ucm := NewUserConcurrencyManager(maxPerUser, maxGlobal)
	
	if ucm.maxPerUser != maxPerUser {
		t.<PERSON><PERSON>("Expected maxPerUser %d, got %d", maxPerUser, ucm.maxPerUser)
	}
	
	if ucm.maxGlobal != maxGlobal {
		t.Errorf("Expected maxGlobal %d, got %d", maxGlobal, ucm.maxGlobal)
	}
	
	if ucm.userLimits == nil {
		t.Error("userLimits should not be nil")
	}
	
	if ucm.globalLimit == nil {
		t.Error("globalLimit should not be nil")
	}
}

func TestAcquireAndReleaseUpload(t *testing.T) {
	ucm := NewUserConcurrencyManager(2, 5)
	ctx := context.Background()
	userID := "test_user"
	
	// 测试获取资源
	err := ucm.AcquireUpload(userID, ctx)
	if err != nil {
		t.Errorf("Failed to acquire upload: %v", err)
	}
	
	// 检查指标
	if ucm.GetUserActiveUploads(userID) != 1 {
		t.Errorf("Expected user active uploads 1, got %d", ucm.GetUserActiveUploads(userID))
	}
	
	if ucm.GetGlobalActiveUploads() != 1 {
		t.Errorf("Expected global active uploads 1, got %d", ucm.GetGlobalActiveUploads())
	}
	
	// 测试释放资源
	ucm.ReleaseUpload(userID)
	
	// 检查指标
	if ucm.GetUserActiveUploads(userID) != 0 {
		t.Errorf("Expected user active uploads 0, got %d", ucm.GetUserActiveUploads(userID))
	}
	
	if ucm.GetGlobalActiveUploads() != 0 {
		t.Errorf("Expected global active uploads 0, got %d", ucm.GetGlobalActiveUploads())
	}
}

func TestUserConcurrencyLimit(t *testing.T) {
	maxPerUser := int64(2)
	ucm := NewUserConcurrencyManager(maxPerUser, 10)
	userID := "test_user"

	// 获取最大允许的资源
	for i := int64(0); i < maxPerUser; i++ {
		ctx := context.Background()
		err := ucm.AcquireUpload(userID, ctx)
		if err != nil {
			t.Errorf("Failed to acquire upload %d: %v", i, err)
		}
	}

	// 尝试获取超出限制的资源（使用超时上下文）
	ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
	defer cancel()
	err := ucm.AcquireUpload(userID, ctx)
	if err == nil {
		t.Error("Expected error when exceeding user limit, got nil")
	}

	// 释放一个资源
	ucm.ReleaseUpload(userID)

	// 现在应该能够再次获取资源
	ctx2 := context.Background()
	err = ucm.AcquireUpload(userID, ctx2)
	if err != nil {
		t.Errorf("Failed to acquire upload after release: %v", err)
	}

	// 清理
	ucm.ReleaseUpload(userID)
	ucm.ReleaseUpload(userID)
}

func TestGlobalConcurrencyLimit(t *testing.T) {
	maxGlobal := int64(3)
	ucm := NewUserConcurrencyManager(5, maxGlobal)

	// 使用不同用户获取全局资源
	for i := int64(0); i < maxGlobal; i++ {
		userID := fmt.Sprintf("user_%d", i)
		ctx := context.Background()
		err := ucm.AcquireUpload(userID, ctx)
		if err != nil {
			t.Errorf("Failed to acquire upload for user %s: %v", userID, err)
		}
	}

	// 尝试获取超出全局限制的资源（使用超时上下文）
	ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
	defer cancel()
	err := ucm.AcquireUpload("extra_user", ctx)
	if err == nil {
		t.Error("Expected error when exceeding global limit, got nil")
	}

	// 释放一个资源
	ucm.ReleaseUpload("user_0")

	// 现在应该能够再次获取资源
	ctx2 := context.Background()
	err = ucm.AcquireUpload("extra_user", ctx2)
	if err != nil {
		t.Errorf("Failed to acquire upload after release: %v", err)
	}

	// 清理
	for i := int64(1); i < maxGlobal; i++ {
		userID := fmt.Sprintf("user_%d", i)
		ucm.ReleaseUpload(userID)
	}
	ucm.ReleaseUpload("extra_user")
}

func TestConcurrentAccess(t *testing.T) {
	ucm := NewUserConcurrencyManager(3, 10)
	ctx := context.Background()
	
	var wg sync.WaitGroup
	errors := make(chan error, 20)
	
	// 启动多个goroutine并发获取资源
	for i := 0; i < 20; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			userID := fmt.Sprintf("user_%d", id%5) // 5个不同用户
			
			err := ucm.AcquireUpload(userID, ctx)
			if err != nil {
				errors <- err
				return
			}
			
			// 模拟上传工作
			time.Sleep(10 * time.Millisecond)
			
			ucm.ReleaseUpload(userID)
		}(i)
	}
	
	wg.Wait()
	close(errors)
	
	// 检查是否有意外错误
	errorCount := 0
	for err := range errors {
		if err != nil {
			errorCount++
			t.Logf("Concurrent access error: %v", err)
		}
	}
	
	// 最终应该没有活跃上传
	if ucm.GetGlobalActiveUploads() != 0 {
		t.Errorf("Expected 0 active uploads after concurrent test, got %d", ucm.GetGlobalActiveUploads())
	}
}

func TestMetrics(t *testing.T) {
	ucm := NewUserConcurrencyManager(2, 5)
	ctx := context.Background()
	
	// 获取一些资源
	ucm.AcquireUpload("user1", ctx)
	ucm.AcquireUpload("user1", ctx)
	ucm.AcquireUpload("user2", ctx)
	
	metrics := ucm.GetMetrics()
	
	if metrics.TotalActiveUploads != 3 {
		t.Errorf("Expected total active uploads 3, got %d", metrics.TotalActiveUploads)
	}
	
	if metrics.ActiveUploads["user1"] != 2 {
		t.Errorf("Expected user1 active uploads 2, got %d", metrics.ActiveUploads["user1"])
	}
	
	if metrics.ActiveUploads["user2"] != 1 {
		t.Errorf("Expected user2 active uploads 1, got %d", metrics.ActiveUploads["user2"])
	}
	
	// 释放资源
	ucm.ReleaseUpload("user1")
	ucm.ReleaseUpload("user1")
	ucm.ReleaseUpload("user2")
	
	// 检查最终指标
	finalMetrics := ucm.GetMetrics()
	if finalMetrics.TotalActiveUploads != 0 {
		t.Errorf("Expected final total active uploads 0, got %d", finalMetrics.TotalActiveUploads)
	}
}

func TestCleanupIdleUsers(t *testing.T) {
	ucm := NewUserConcurrencyManager(2, 5)
	ctx := context.Background()
	
	// 创建一些用户信号量
	ucm.AcquireUpload("user1", ctx)
	ucm.AcquireUpload("user2", ctx)
	ucm.AcquireUpload("user3", ctx)
	
	// 释放所有资源
	ucm.ReleaseUpload("user1")
	ucm.ReleaseUpload("user2")
	ucm.ReleaseUpload("user3")
	
	// 检查用户信号量是否存在
	ucm.mu.RLock()
	userLimitCount := len(ucm.userLimits)
	ucm.mu.RUnlock()
	
	if userLimitCount != 3 {
		t.Errorf("Expected 3 user limits before cleanup, got %d", userLimitCount)
	}
	
	// 执行清理
	ucm.CleanupIdleUsers()
	
	// 检查清理后的状态
	ucm.mu.RLock()
	userLimitCountAfter := len(ucm.userLimits)
	ucm.mu.RUnlock()
	
	if userLimitCountAfter != 0 {
		t.Errorf("Expected 0 user limits after cleanup, got %d", userLimitCountAfter)
	}
}

// 性能测试
func BenchmarkAcquireRelease(b *testing.B) {
	ucm := NewUserConcurrencyManager(10, 100)
	ctx := context.Background()
	
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		userID := "bench_user"
		for pb.Next() {
			ucm.AcquireUpload(userID, ctx)
			ucm.ReleaseUpload(userID)
		}
	})
}
