package retry

import (
	"context"
	"fmt"
	"math"
	"math/rand"
	"net"
	"net/http"
	"strings"
	"syscall"
	"time"

	log "github.com/sirupsen/logrus"
)

// RetryPolicy 重试策略
type RetryPolicy struct {
	MaxRetries      int           // 最大重试次数
	BaseDelay       time.Duration // 基础延迟
	MaxDelay        time.Duration // 最大延迟
	BackoffFactor   float64       // 退避因子
	JitterEnabled   bool          // 是否启用抖动
	RetryableErrors []ErrorType   // 可重试的错误类型
}

// ErrorType 错误类型
type ErrorType int

const (
	ErrorTypeNetwork ErrorType = iota
	ErrorTypeTimeout
	ErrorTypeServerError
	ErrorTypeRateLimit
	ErrorTypeTemporary
)

// RetryableError 可重试的错误
type RetryableError struct {
	Err       error
	Type      ErrorType
	Retryable bool
	Delay     time.Duration
}

func (re *RetryableError) Error() string {
	return fmt.Sprintf("retryable error (type: %d, retryable: %t): %v", re.Type, re.Retryable, re.Err)
}

// HTTPError HTTP错误包装器
type HTTPError struct {
	Response *http.Response
	Message  string
}

func (he *HTTPError) Error() string {
	if he.Message != "" {
		return he.Message
	}
	return fmt.Sprintf("HTTP error: %d %s", he.Response.StatusCode, he.Response.Status)
}

// NewHTTPError 创建HTTP错误
func NewHTTPError(resp *http.Response, message string) *HTTPError {
	return &HTTPError{
		Response: resp,
		Message:  message,
	}
}

// RetryMetrics 重试指标
type RetryMetrics struct {
	TotalAttempts    int64
	SuccessfulRetries int64
	FailedRetries    int64
	AverageDelay     time.Duration
	MaxDelay         time.Duration
	ErrorDistribution map[ErrorType]int64
}

// DefaultRetryPolicy 创建默认重试策略
func DefaultRetryPolicy() *RetryPolicy {
	return &RetryPolicy{
		MaxRetries:    3,
		BaseDelay:     1 * time.Second,
		MaxDelay:      30 * time.Second,
		BackoffFactor: 2.0,
		JitterEnabled: true,
		RetryableErrors: []ErrorType{
			ErrorTypeNetwork,
			ErrorTypeTimeout,
			ErrorTypeServerError,
			ErrorTypeRateLimit,
			ErrorTypeTemporary,
		},
	}
}

// UploadRetryPolicy 创建上传专用重试策略
func UploadRetryPolicy() *RetryPolicy {
	return &RetryPolicy{
		MaxRetries:    5,
		BaseDelay:     500 * time.Millisecond,
		MaxDelay:      60 * time.Second,
		BackoffFactor: 1.5,
		JitterEnabled: true,
		RetryableErrors: []ErrorType{
			ErrorTypeNetwork,
			ErrorTypeTimeout,
			ErrorTypeServerError,
			ErrorTypeRateLimit,
		},
	}
}

// Execute 执行带重试的操作
func (rp *RetryPolicy) Execute(ctx context.Context, operation func() error) error {
	var lastErr error
	metrics := &RetryMetrics{
		ErrorDistribution: make(map[ErrorType]int64),
	}
	
	for attempt := 0; attempt <= rp.MaxRetries; attempt++ {
		metrics.TotalAttempts++
		
		// 第一次尝试不延迟
		if attempt > 0 {
			delay := rp.calculateDelay(attempt)
			metrics.AverageDelay = time.Duration((int64(metrics.AverageDelay)*int64(attempt-1) + int64(delay)) / int64(attempt))
			if delay > metrics.MaxDelay {
				metrics.MaxDelay = delay
			}
			
			log.Debugf("Retry attempt %d/%d, waiting %v", attempt, rp.MaxRetries, delay)
			
			select {
			case <-time.After(delay):
			case <-ctx.Done():
				return fmt.Errorf("context cancelled during retry delay: %w", ctx.Err())
			}
		}
		
		// 执行操作
		err := operation()
		if err == nil {
			if attempt > 0 {
				metrics.SuccessfulRetries++
				log.Infof("Operation succeeded after %d retries", attempt)
			}
			return nil
		}
		
		lastErr = err
		
		// 分析错误类型
		retryableErr := rp.analyzeError(err)
		metrics.ErrorDistribution[retryableErr.Type]++
		
		// 判断是否应该重试
		if !retryableErr.Retryable || attempt >= rp.MaxRetries {
			if attempt >= rp.MaxRetries {
				metrics.FailedRetries++
				log.Warnf("Operation failed after %d attempts: %v", rp.MaxRetries+1, err)
			} else {
				log.Warnf("Operation failed with non-retryable error: %v", err)
			}
			break
		}
		
		log.Debugf("Retryable error detected (attempt %d/%d): %v", attempt+1, rp.MaxRetries+1, err)
	}
	
	return fmt.Errorf("operation failed after %d attempts: %w", rp.MaxRetries+1, lastErr)
}

// ExecuteWithCallback 执行带重试和回调的操作
func (rp *RetryPolicy) ExecuteWithCallback(ctx context.Context, operation func() error, 
	onRetry func(attempt int, err error, delay time.Duration)) error {
	
	var lastErr error
	
	for attempt := 0; attempt <= rp.MaxRetries; attempt++ {
		// 第一次尝试不延迟
		if attempt > 0 {
			delay := rp.calculateDelay(attempt)
			
			if onRetry != nil {
				onRetry(attempt, lastErr, delay)
			}
			
			select {
			case <-time.After(delay):
			case <-ctx.Done():
				return fmt.Errorf("context cancelled during retry delay: %w", ctx.Err())
			}
		}
		
		// 执行操作
		err := operation()
		if err == nil {
			return nil
		}
		
		lastErr = err
		
		// 判断是否应该重试
		retryableErr := rp.analyzeError(err)
		if !retryableErr.Retryable || attempt >= rp.MaxRetries {
			break
		}
	}
	
	return fmt.Errorf("operation failed after %d attempts: %w", rp.MaxRetries+1, lastErr)
}

// calculateDelay 计算延迟时间
func (rp *RetryPolicy) calculateDelay(attempt int) time.Duration {
	// 指数退避
	delay := time.Duration(float64(rp.BaseDelay) * math.Pow(rp.BackoffFactor, float64(attempt-1)))
	
	// 限制最大延迟
	if delay > rp.MaxDelay {
		delay = rp.MaxDelay
	}
	
	// 添加抖动
	if rp.JitterEnabled {
		jitter := time.Duration(rand.Float64() * float64(delay) * 0.1) // 10%的抖动
		delay += jitter
	}
	
	return delay
}

// analyzeError 分析错误类型
func (rp *RetryPolicy) analyzeError(err error) *RetryableError {
	if err == nil {
		return &RetryableError{Err: err, Type: ErrorTypeNetwork, Retryable: false}
	}
	
	errStr := strings.ToLower(err.Error())

	// 超时错误 - 优先检查，因为超时错误也可能是网络错误
	if isTimeoutError(err) {
		return &RetryableError{
			Err:       err,
			Type:      ErrorTypeTimeout,
			Retryable: rp.isRetryableType(ErrorTypeTimeout),
		}
	}

	// 网络错误
	if isNetworkError(err) {
		return &RetryableError{
			Err:       err,
			Type:      ErrorTypeNetwork,
			Retryable: rp.isRetryableType(ErrorTypeNetwork),
		}
	}
	
	// HTTP错误 - 需要自定义HTTP错误类型
	if httpErr, ok := err.(*HTTPError); ok {
		return rp.analyzeHTTPError(httpErr.Response)
	}
	
	// 临时错误
	if isTemporaryError(err) {
		return &RetryableError{
			Err:       err,
			Type:      ErrorTypeTemporary,
			Retryable: rp.isRetryableType(ErrorTypeTemporary),
		}
	}
	
	// 特定错误字符串匹配
	if strings.Contains(errStr, "connection refused") ||
		strings.Contains(errStr, "connection reset") ||
		strings.Contains(errStr, "broken pipe") ||
		strings.Contains(errStr, "no route to host") {
		return &RetryableError{
			Err:       err,
			Type:      ErrorTypeNetwork,
			Retryable: rp.isRetryableType(ErrorTypeNetwork),
		}
	}

	if strings.Contains(errStr, "timeout") ||
		strings.Contains(errStr, "deadline exceeded") {
		return &RetryableError{
			Err:       err,
			Type:      ErrorTypeTimeout,
			Retryable: rp.isRetryableType(ErrorTypeTimeout),
		}
	}

	// 检查是否包含"temporary"或"failure"等可能表示临时错误的关键词
	if strings.Contains(errStr, "temporary") ||
		strings.Contains(errStr, "failure") ||
		strings.Contains(errStr, "unavailable") ||
		strings.Contains(errStr, "retry") {
		return &RetryableError{
			Err:       err,
			Type:      ErrorTypeTemporary,
			Retryable: rp.isRetryableType(ErrorTypeTemporary),
		}
	}

	// 默认不重试
	return &RetryableError{
		Err:       err,
		Type:      ErrorTypeNetwork,
		Retryable: false,
	}
}

// analyzeHTTPError 分析HTTP错误
func (rp *RetryPolicy) analyzeHTTPError(resp *http.Response) *RetryableError {
	switch resp.StatusCode {
	case 429: // Too Many Requests
		return &RetryableError{
			Err:       fmt.Errorf("rate limited: %d", resp.StatusCode),
			Type:      ErrorTypeRateLimit,
			Retryable: rp.isRetryableType(ErrorTypeRateLimit),
		}
	case 500, 502, 503, 504: // Server errors
		return &RetryableError{
			Err:       fmt.Errorf("server error: %d", resp.StatusCode),
			Type:      ErrorTypeServerError,
			Retryable: rp.isRetryableType(ErrorTypeServerError),
		}
	default:
		// 4xx errors are generally not retryable
		return &RetryableError{
			Err:       fmt.Errorf("client error: %d", resp.StatusCode),
			Type:      ErrorTypeNetwork,
			Retryable: false,
		}
	}
}

// isRetryableType 检查错误类型是否可重试
func (rp *RetryPolicy) isRetryableType(errorType ErrorType) bool {
	for _, retryableType := range rp.RetryableErrors {
		if retryableType == errorType {
			return true
		}
	}
	return false
}

// isNetworkError 检查是否为网络错误
func isNetworkError(err error) bool {
	if netErr, ok := err.(net.Error); ok {
		return netErr.Temporary() || netErr.Timeout()
	}
	
	// 检查系统调用错误
	if opErr, ok := err.(*net.OpError); ok {
		if syscallErr, ok := opErr.Err.(*syscall.Errno); ok {
			switch *syscallErr {
			case syscall.ECONNREFUSED, syscall.ECONNRESET, syscall.EPIPE:
				return true
			}
		}
	}
	
	return false
}

// isTimeoutError 检查是否为超时错误
func isTimeoutError(err error) bool {
	if netErr, ok := err.(net.Error); ok {
		return netErr.Timeout()
	}

	// 检查自定义超时接口
	if timeoutErr, ok := err.(interface{ Timeout() bool }); ok {
		return timeoutErr.Timeout()
	}

	return strings.Contains(strings.ToLower(err.Error()), "timeout")
}

// isTemporaryError 检查是否为临时错误
func isTemporaryError(err error) bool {
	if tempErr, ok := err.(interface{ Temporary() bool }); ok {
		return tempErr.Temporary()
	}
	
	return false
}
