package retry

import (
	"context"
	"errors"
	"fmt"
	"net"
	"net/http"
	"testing"
	"time"
)

func TestDefaultRetryPolicy(t *testing.T) {
	policy := DefaultRetryPolicy()
	
	if policy.MaxRetries != 3 {
		t.<PERSON><PERSON><PERSON>("Expected MaxRetries 3, got %d", policy.MaxRetries)
	}
	
	if policy.BaseDelay != 1*time.Second {
		t.<PERSON><PERSON>("Expected BaseDelay 1s, got %v", policy.BaseDelay)
	}
	
	if !policy.JitterEnabled {
		t.<PERSON><PERSON>("Expected JitterEnabled to be true")
	}
}

func TestUploadRetryPolicy(t *testing.T) {
	policy := UploadRetryPolicy()
	
	if policy.MaxRetries != 5 {
		t.<PERSON><PERSON><PERSON>("Expected MaxRetries 5, got %d", policy.MaxRetries)
	}
	
	if policy.BaseDelay != 500*time.Millisecond {
		t.Errorf("Expected BaseDelay 500ms, got %v", policy.BaseDelay)
	}
}

func TestRetryPolicyExecuteSuccess(t *testing.T) {
	policy := DefaultRetryPolicy()
	policy.BaseDelay = 10 * time.Millisecond // 加速测试
	
	attempts := 0
	operation := func() error {
		attempts++
		if attempts < 3 {
			return errors.New("temporary failure")
		}
		return nil
	}
	
	ctx := context.Background()
	err := policy.Execute(ctx, operation)
	
	if err != nil {
		t.Errorf("Expected success, got error: %v", err)
	}
	
	if attempts != 3 {
		t.Errorf("Expected 3 attempts, got %d", attempts)
	}
}

func TestRetryPolicyExecuteFailure(t *testing.T) {
	policy := DefaultRetryPolicy()
	policy.BaseDelay = 10 * time.Millisecond // 加速测试
	policy.MaxRetries = 2
	
	attempts := 0
	operation := func() error {
		attempts++
		return errors.New("persistent failure")
	}
	
	ctx := context.Background()
	err := policy.Execute(ctx, operation)
	
	if err == nil {
		t.Error("Expected failure, got success")
	}
	
	expectedAttempts := policy.MaxRetries + 1
	if attempts != expectedAttempts {
		t.Errorf("Expected %d attempts, got %d", expectedAttempts, attempts)
	}
}

func TestRetryPolicyContextCancellation(t *testing.T) {
	policy := DefaultRetryPolicy()
	policy.BaseDelay = 1 * time.Second // 长延迟
	
	attempts := 0
	operation := func() error {
		attempts++
		return errors.New("failure")
	}
	
	ctx, cancel := context.WithTimeout(context.Background(), 50*time.Millisecond)
	defer cancel()
	
	err := policy.Execute(ctx, operation)
	
	if err == nil {
		t.Error("Expected context cancellation error")
	}
	
	if attempts > 2 {
		t.Errorf("Expected at most 2 attempts due to context cancellation, got %d", attempts)
	}
}

func TestRetryPolicyCalculateDelay(t *testing.T) {
	policy := &RetryPolicy{
		BaseDelay:     100 * time.Millisecond,
		MaxDelay:      1 * time.Second,
		BackoffFactor: 2.0,
		JitterEnabled: false, // 禁用抖动以便测试
	}
	
	testCases := []struct {
		attempt      int
		expectedMin  time.Duration
		expectedMax  time.Duration
	}{
		{1, 100 * time.Millisecond, 100 * time.Millisecond},
		{2, 200 * time.Millisecond, 200 * time.Millisecond},
		{3, 400 * time.Millisecond, 400 * time.Millisecond},
		{4, 800 * time.Millisecond, 800 * time.Millisecond},
		{5, 1 * time.Second, 1 * time.Second}, // 受MaxDelay限制
	}
	
	for _, tc := range testCases {
		delay := policy.calculateDelay(tc.attempt)
		if delay < tc.expectedMin || delay > tc.expectedMax {
			t.Errorf("Attempt %d: expected delay between %v and %v, got %v", 
				tc.attempt, tc.expectedMin, tc.expectedMax, delay)
		}
	}
}

func TestRetryPolicyAnalyzeError(t *testing.T) {
	policy := DefaultRetryPolicy()
	
	testCases := []struct {
		name      string
		err       error
		errorType ErrorType
		retryable bool
	}{
		{
			name:      "network error",
			err:       &net.OpError{Op: "dial", Err: errors.New("connection refused")},
			errorType: ErrorTypeNetwork,
			retryable: true,
		},
		{
			name:      "timeout error",
			err:       &timeoutError{},
			errorType: ErrorTypeTimeout,
			retryable: true,
		},
		{
			name:      "generic error",
			err:       errors.New("some other error"),
			errorType: ErrorTypeNetwork,
			retryable: false,
		},
		{
			name:      "connection refused",
			err:       errors.New("connection refused"),
			errorType: ErrorTypeNetwork,
			retryable: true,
		},
		{
			name:      "timeout in message",
			err:       errors.New("operation timeout"),
			errorType: ErrorTypeTimeout,
			retryable: true,
		},
	}
	
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			retryableErr := policy.analyzeError(tc.err)
			
			if retryableErr.Type != tc.errorType {
				t.Errorf("Expected error type %d, got %d", tc.errorType, retryableErr.Type)
			}
			
			if retryableErr.Retryable != tc.retryable {
				t.Errorf("Expected retryable %t, got %t", tc.retryable, retryableErr.Retryable)
			}
		})
	}
}

func TestRetryPolicyAnalyzeHTTPError(t *testing.T) {
	policy := DefaultRetryPolicy()
	
	testCases := []struct {
		statusCode int
		errorType  ErrorType
		retryable  bool
	}{
		{429, ErrorTypeRateLimit, true},
		{500, ErrorTypeServerError, true},
		{502, ErrorTypeServerError, true},
		{503, ErrorTypeServerError, true},
		{504, ErrorTypeServerError, true},
		{400, ErrorTypeNetwork, false},
		{401, ErrorTypeNetwork, false},
		{404, ErrorTypeNetwork, false},
	}
	
	for _, tc := range testCases {
		t.Run(fmt.Sprintf("HTTP_%d", tc.statusCode), func(t *testing.T) {
			resp := &http.Response{StatusCode: tc.statusCode}
			retryableErr := policy.analyzeHTTPError(resp)
			
			if retryableErr.Type != tc.errorType {
				t.Errorf("Expected error type %d, got %d", tc.errorType, retryableErr.Type)
			}
			
			if retryableErr.Retryable != tc.retryable {
				t.Errorf("Expected retryable %t, got %t", tc.retryable, retryableErr.Retryable)
			}
		})
	}
}

func TestRetryPolicyExecuteWithCallback(t *testing.T) {
	policy := DefaultRetryPolicy()
	policy.BaseDelay = 10 * time.Millisecond // 加速测试
	
	attempts := 0
	callbackCalls := 0
	
	operation := func() error {
		attempts++
		if attempts < 3 {
			return errors.New("temporary failure")
		}
		return nil
	}
	
	onRetry := func(attempt int, err error, delay time.Duration) {
		callbackCalls++
		if attempt <= 0 {
			t.Errorf("Expected positive attempt number, got %d", attempt)
		}
		if err == nil {
			t.Error("Expected error in callback, got nil")
		}
		if delay <= 0 {
			t.Errorf("Expected positive delay, got %v", delay)
		}
	}
	
	ctx := context.Background()
	err := policy.ExecuteWithCallback(ctx, operation, onRetry)
	
	if err != nil {
		t.Errorf("Expected success, got error: %v", err)
	}
	
	if attempts != 3 {
		t.Errorf("Expected 3 attempts, got %d", attempts)
	}
	
	if callbackCalls != 2 { // 回调应该被调用2次（第2和第3次尝试前）
		t.Errorf("Expected 2 callback calls, got %d", callbackCalls)
	}
}

func TestRetryPolicyJitter(t *testing.T) {
	policy := &RetryPolicy{
		BaseDelay:     100 * time.Millisecond,
		MaxDelay:      1 * time.Second,
		BackoffFactor: 2.0,
		JitterEnabled: true,
	}
	
	// 测试抖动是否生效
	delays := make([]time.Duration, 10)
	for i := 0; i < 10; i++ {
		delays[i] = policy.calculateDelay(2) // 第2次重试
	}
	
	// 检查是否有不同的延迟值（抖动效果）
	allSame := true
	for i := 1; i < len(delays); i++ {
		if delays[i] != delays[0] {
			allSame = false
			break
		}
	}
	
	if allSame {
		t.Error("Expected different delays due to jitter, but all delays were the same")
	}
}

// 辅助类型用于测试
type timeoutError struct{}

func (e *timeoutError) Error() string   { return "timeout" }
func (e *timeoutError) Timeout() bool   { return true }
func (e *timeoutError) Temporary() bool { return true }

// 性能测试
func BenchmarkRetryPolicyExecute(b *testing.B) {
	policy := DefaultRetryPolicy()
	policy.BaseDelay = 1 * time.Millisecond
	
	operation := func() error {
		return nil // 总是成功
	}
	
	ctx := context.Background()
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		policy.Execute(ctx, operation)
	}
}

func BenchmarkRetryPolicyAnalyzeError(b *testing.B) {
	policy := DefaultRetryPolicy()
	err := errors.New("connection refused")
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		policy.analyzeError(err)
	}
}
