package bootstrap

import (
	"github.com/alist-org/alist/v3/internal/concurrency"
	"github.com/alist-org/alist/v3/internal/conf"
	"github.com/alist-org/alist/v3/internal/memory"
	"github.com/alist-org/alist/v3/internal/resume"
	"github.com/alist-org/alist/v3/internal/streaming"
	log "github.com/sirupsen/logrus"
)

// InitConcurrency 初始化并发控制
func InitConcurrency() {
	log.Info("=== Starting upload concurrency control initialization ===")
	log.Info("🚀 BOOTSTRAP: InitConcurrency function called!")

	// 临时测试：确认函数被调用
	// panic("InitConcurrency called - this is a test")

	// 从配置中获取并发限制参数，如果没有配置则使用默认值
	maxPerUser := int64(5)  // 每用户最大并发上传数
	maxGlobal := int64(50)  // 全局最大并发上传数
	
	// 尝试从配置文件读取自定义值
	if conf.Conf.MaxConcurrentUploadsPerUser > 0 {
		maxPerUser = int64(conf.Conf.MaxConcurrentUploadsPerUser)
	}
	
	if conf.Conf.MaxConcurrentUploadsGlobal > 0 {
		maxGlobal = int64(conf.Conf.MaxConcurrentUploadsGlobal)
	}
	
	// 确保全局限制不小于单用户限制
	if maxGlobal < maxPerUser {
		maxGlobal = maxPerUser * 10
		log.Warnf("Global upload limit (%d) is less than per-user limit (%d), adjusting to %d", 
			maxGlobal/10, maxPerUser, maxGlobal)
	}
	
	// 初始化并发管理器
	concurrency.InitConcurrencyManager(maxPerUser, maxGlobal)

	// 初始化内存缓冲区池
	maxMemoryMB := int64(100) // 默认100MB
	if conf.Conf.MaxBufferPoolMemoryMB > 0 {
		maxMemoryMB = int64(conf.Conf.MaxBufferPoolMemoryMB)
	}
	memory.InitBufferPool(maxMemoryMB)

	// 初始化断点续传
	resume.InitResumeStorage()
	resume.InitProgressManager()

	// 初始化流式上传
	streaming.InitStreamingUploadManager()

	log.Infof("Upload concurrency control initialized: maxPerUser=%d, maxGlobal=%d",
		maxPerUser, maxGlobal)
	log.Infof("Buffer pool initialized with max memory: %d MB", maxMemoryMB)
	log.Info("Resumable upload system initialized")
	log.Info("Streaming upload system initialized")
}
