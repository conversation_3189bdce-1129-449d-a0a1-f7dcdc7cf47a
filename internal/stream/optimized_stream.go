package stream

import (
	"context"
	"fmt"
	"io"
	"sync"
	"time"

	"github.com/alist-org/alist/v3/internal/memory"
	log "github.com/sirupsen/logrus"
)

// OptimizedFileStream 内存优化的文件流
type OptimizedFileStream struct {
	*FileStream                    // 嵌入原始FileStream
	bufferPool     *memory.BufferPool // 缓冲区池
	chunkSize      int64              // 分片大小
	maxConcurrent  int                // 最大并发分片数
	useStreaming   bool               // 是否使用流式处理
	metrics        *StreamMetrics     // 流处理指标
}

// StreamMetrics 流处理指标
type StreamMetrics struct {
	TotalBytesProcessed int64         // 总处理字节数
	ChunksProcessed     int64         // 处理的分片数
	AverageChunkTime    time.Duration // 平均分片处理时间
	PeakMemoryUsage     int64         // 峰值内存使用
	ErrorCount          int64         // 错误计数
	mu                  sync.RWMutex  // 指标读写锁
}

// ChunkProcessor 分片处理器
type ChunkProcessor struct {
	buffer    []byte
	offset    int64
	size      int64
	processed bool
	error     error
}

// NewOptimizedFileStream 创建内存优化的文件流
func NewOptimizedFileStream(original *FileStream, chunkSizeMB int64, maxConcurrent int) *OptimizedFileStream {
	chunkSize := chunkSizeMB * 1024 * 1024 // 转换为字节
	
	// 根据文件大小决定是否使用流式处理
	useStreaming := original.GetSize() > 10*1024*1024 // 大于10MB使用流式处理
	
	return &OptimizedFileStream{
		FileStream:    original,
		bufferPool:    memory.GlobalBufferPool,
		chunkSize:     chunkSize,
		maxConcurrent: maxConcurrent,
		useStreaming:  useStreaming,
		metrics: &StreamMetrics{},
	}
}

// ReadChunk 读取指定大小的数据块
func (ofs *OptimizedFileStream) ReadChunk(size int64) ([]byte, error) {
	if ofs.bufferPool == nil {
		// 如果没有缓冲区池，使用传统方式
		return ofs.readChunkTraditional(size)
	}
	
	// 从缓冲区池获取缓冲区
	buffer, err := ofs.bufferPool.Get(int(size))
	if err != nil {
		log.Warnf("Failed to get buffer from pool: %v, falling back to traditional method", err)
		return ofs.readChunkTraditional(size)
	}
	
	// 读取数据
	n, err := ofs.Reader.Read(buffer)
	if err != nil && err != io.EOF {
		// 归还缓冲区
		ofs.bufferPool.Put(buffer)
		return nil, err
	}
	
	// 更新指标
	ofs.updateMetrics(int64(n))
	
	// 返回实际读取的数据
	if n < len(buffer) {
		// 创建新的切片，归还原缓冲区
		result := make([]byte, n)
		copy(result, buffer[:n])
		ofs.bufferPool.Put(buffer)
		return result, err
	}
	
	return buffer[:n], err
}

// readChunkTraditional 传统方式读取数据块
func (ofs *OptimizedFileStream) readChunkTraditional(size int64) ([]byte, error) {
	buffer := make([]byte, size)
	n, err := ofs.Reader.Read(buffer)
	if err != nil && err != io.EOF {
		return nil, err
	}
	
	ofs.updateMetrics(int64(n))
	return buffer[:n], err
}

// ProcessInChunks 分片处理文件流
func (ofs *OptimizedFileStream) ProcessInChunks(ctx context.Context, processor func([]byte, int64) error) error {
	if !ofs.useStreaming {
		// 小文件直接处理
		return ofs.processSmallFile(ctx, processor)
	}
	
	// 大文件分片处理
	return ofs.processLargeFile(ctx, processor)
}

// processSmallFile 处理小文件
func (ofs *OptimizedFileStream) processSmallFile(ctx context.Context, processor func([]byte, int64) error) error {
	data, err := ofs.ReadChunk(ofs.GetSize())
	if err != nil {
		return fmt.Errorf("failed to read small file: %w", err)
	}
	
	defer func() {
		if ofs.bufferPool != nil {
			ofs.bufferPool.Put(data)
		}
	}()
	
	return processor(data, 0)
}

// processLargeFile 处理大文件
func (ofs *OptimizedFileStream) processLargeFile(ctx context.Context, processor func([]byte, int64) error) error {
	fileSize := ofs.GetSize()
	numChunks := (fileSize + ofs.chunkSize - 1) / ofs.chunkSize
	
	// 创建分片处理器通道
	chunkChan := make(chan *ChunkProcessor, ofs.maxConcurrent)
	errorChan := make(chan error, numChunks)
	
	// 启动工作协程
	var wg sync.WaitGroup
	for i := 0; i < ofs.maxConcurrent; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			ofs.chunkWorker(ctx, chunkChan, processor, errorChan)
		}()
	}
	
	// 发送分片任务
	go func() {
		defer close(chunkChan)
		
		var offset int64
		for chunkIndex := int64(0); chunkIndex < numChunks; chunkIndex++ {
			select {
			case <-ctx.Done():
				return
			default:
			}
			
			chunkSize := ofs.chunkSize
			if offset+chunkSize > fileSize {
				chunkSize = fileSize - offset
			}
			
			// 读取分片数据
			data, err := ofs.ReadChunk(chunkSize)
			if err != nil {
				errorChan <- fmt.Errorf("failed to read chunk %d: %w", chunkIndex, err)
				return
			}
			
			chunk := &ChunkProcessor{
				buffer: data,
				offset: offset,
				size:   chunkSize,
			}
			
			select {
			case chunkChan <- chunk:
				offset += chunkSize
			case <-ctx.Done():
				// 归还缓冲区
				if ofs.bufferPool != nil {
					ofs.bufferPool.Put(data)
				}
				return
			}
		}
	}()
	
	// 等待所有工作协程完成
	wg.Wait()
	close(errorChan)
	
	// 检查错误
	for err := range errorChan {
		if err != nil {
			return err
		}
	}
	
	return nil
}

// chunkWorker 分片处理工作协程
func (ofs *OptimizedFileStream) chunkWorker(ctx context.Context, chunkChan <-chan *ChunkProcessor, 
	processor func([]byte, int64) error, errorChan chan<- error) {
	
	for chunk := range chunkChan {
		select {
		case <-ctx.Done():
			// 归还缓冲区
			if ofs.bufferPool != nil {
				ofs.bufferPool.Put(chunk.buffer)
			}
			return
		default:
		}
		
		startTime := time.Now()
		
		// 处理分片
		err := processor(chunk.buffer, chunk.offset)
		if err != nil {
			chunk.error = err
			errorChan <- fmt.Errorf("chunk processing failed at offset %d: %w", chunk.offset, err)
		} else {
			chunk.processed = true
		}
		
		// 归还缓冲区
		if ofs.bufferPool != nil {
			ofs.bufferPool.Put(chunk.buffer)
		}
		
		// 更新处理时间指标
		ofs.updateChunkTime(time.Since(startTime))
	}
}

// updateMetrics 更新流处理指标
func (ofs *OptimizedFileStream) updateMetrics(bytesProcessed int64) {
	ofs.metrics.mu.Lock()
	defer ofs.metrics.mu.Unlock()
	
	ofs.metrics.TotalBytesProcessed += bytesProcessed
	ofs.metrics.ChunksProcessed++
	
	// 更新峰值内存使用
	if ofs.bufferPool != nil {
		currentMem := ofs.bufferPool.GetCurrentMemoryUsage()
		if currentMem > ofs.metrics.PeakMemoryUsage {
			ofs.metrics.PeakMemoryUsage = currentMem
		}
	}
}

// updateChunkTime 更新分片处理时间
func (ofs *OptimizedFileStream) updateChunkTime(duration time.Duration) {
	ofs.metrics.mu.Lock()
	defer ofs.metrics.mu.Unlock()
	
	// 计算移动平均值
	if ofs.metrics.ChunksProcessed > 0 {
		ofs.metrics.AverageChunkTime = time.Duration(
			(int64(ofs.metrics.AverageChunkTime)*ofs.metrics.ChunksProcessed + int64(duration)) /
			(ofs.metrics.ChunksProcessed + 1))
	} else {
		ofs.metrics.AverageChunkTime = duration
	}
}

// GetMetrics 获取流处理指标
func (ofs *OptimizedFileStream) GetMetrics() *StreamMetrics {
	ofs.metrics.mu.RLock()
	defer ofs.metrics.mu.RUnlock()
	
	return &StreamMetrics{
		TotalBytesProcessed: ofs.metrics.TotalBytesProcessed,
		ChunksProcessed:     ofs.metrics.ChunksProcessed,
		AverageChunkTime:    ofs.metrics.AverageChunkTime,
		PeakMemoryUsage:     ofs.metrics.PeakMemoryUsage,
		ErrorCount:          ofs.metrics.ErrorCount,
	}
}

// Close 关闭优化流并清理资源
func (ofs *OptimizedFileStream) Close() error {
	// 调用原始流的关闭方法
	if ofs.FileStream != nil {
		return ofs.FileStream.Close()
	}
	return nil
}

// GetOptimizedStream 获取内存优化的文件流
func GetOptimizedStream(original *FileStream, chunkSizeMB int64, maxConcurrent int) *OptimizedFileStream {
	if memory.GlobalBufferPool == nil {
		log.Warn("Buffer pool not initialized, using traditional stream processing")
		return &OptimizedFileStream{
			FileStream:    original,
			chunkSize:     chunkSizeMB * 1024 * 1024,
			maxConcurrent: maxConcurrent,
			useStreaming:  false,
			metrics:       &StreamMetrics{},
		}
	}
	
	return NewOptimizedFileStream(original, chunkSizeMB, maxConcurrent)
}
