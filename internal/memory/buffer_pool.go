package memory

import (
	"fmt"
	"runtime"
	"sync"
	"sync/atomic"
	"time"

	log "github.com/sirupsen/logrus"
)

// BufferPool 缓冲区池管理器
type BufferPool struct {
	pools       map[int]*sync.Pool // 不同大小的缓冲区池
	sizes       []int              // 支持的缓冲区大小
	maxMemUsage int64              // 最大内存使用量（字节）
	currentMem  int64              // 当前内存使用量
	metrics     *PoolMetrics       // 池指标
	mu          sync.RWMutex       // 保护pools的读写锁
}

// PoolMetrics 缓冲区池指标
type PoolMetrics struct {
	TotalAllocated   int64            // 总分配次数
	TotalReleased    int64            // 总释放次数
	CurrentInUse     int64            // 当前使用中的缓冲区数量
	PeakMemoryUsage  int64            // 峰值内存使用量
	PoolHitRate      float64          // 池命中率
	SizeDistribution map[int]int64    // 各大小缓冲区的使用分布
	mu               sync.RWMutex     // 指标读写锁
}

// 预定义的缓冲区大小（字节）
var defaultSizes = []int{
	1 * 1024,      // 1KB
	4 * 1024,      // 4KB
	16 * 1024,     // 16KB
	64 * 1024,     // 64KB
	256 * 1024,    // 256KB
	1 * 1024 * 1024,   // 1MB
	4 * 1024 * 1024,   // 4MB
	16 * 1024 * 1024,  // 16MB
}

// NewBufferPool 创建新的缓冲区池
func NewBufferPool(maxMemoryMB int64) *BufferPool {
	maxMemUsage := maxMemoryMB * 1024 * 1024 // 转换为字节
	
	bp := &BufferPool{
		pools:       make(map[int]*sync.Pool),
		sizes:       defaultSizes,
		maxMemUsage: maxMemUsage,
		metrics: &PoolMetrics{
			SizeDistribution: make(map[int]int64),
		},
	}
	
	// 初始化各种大小的缓冲区池
	for _, size := range bp.sizes {
		bp.initPool(size)
	}
	
	// 启动内存监控协程
	go bp.startMemoryMonitor()
	
	log.Infof("Buffer pool initialized with max memory: %d MB", maxMemoryMB)
	return bp
}

// initPool 初始化指定大小的缓冲区池
func (bp *BufferPool) initPool(size int) {
	bp.pools[size] = &sync.Pool{
		New: func() interface{} {
			// 检查内存限制
			current := atomic.LoadInt64(&bp.currentMem)
			if current+int64(size) > bp.maxMemUsage {
				return nil // 返回nil表示内存不足
			}

			buffer := make([]byte, size)
			atomic.AddInt64(&bp.currentMem, int64(size))
			atomic.AddInt64(&bp.metrics.TotalAllocated, 1)

			// 更新峰值内存使用量
			newCurrent := atomic.LoadInt64(&bp.currentMem)
			for {
				peak := atomic.LoadInt64(&bp.metrics.PeakMemoryUsage)
				if newCurrent <= peak || atomic.CompareAndSwapInt64(&bp.metrics.PeakMemoryUsage, peak, newCurrent) {
					break
				}
			}

			log.Debugf("Created new buffer of size %d bytes, current memory: %d bytes", size, newCurrent)
			return buffer
		},
	}
}

// Get 获取指定大小的缓冲区
func (bp *BufferPool) Get(size int) ([]byte, error) {
	// 找到最适合的缓冲区大小
	poolSize := bp.findBestSize(size)

	bp.mu.RLock()
	pool, exists := bp.pools[poolSize]
	bp.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("no pool available for size %d", size)
	}

	// 从池中获取缓冲区
	bufferInterface := pool.Get()
	if bufferInterface == nil {
		// 内存不足，无法创建新缓冲区
		return nil, fmt.Errorf("memory limit exceeded: current=%d, max=%d",
			atomic.LoadInt64(&bp.currentMem), bp.maxMemUsage)
	}

	buffer := bufferInterface.([]byte)

	// 更新指标
	atomic.AddInt64(&bp.metrics.CurrentInUse, 1)
	bp.metrics.mu.Lock()
	bp.metrics.SizeDistribution[poolSize]++
	bp.metrics.mu.Unlock()

	// 如果需要的大小小于缓冲区大小，返回切片
	if size <= len(buffer) {
		return buffer[:size], nil
	}

	return buffer, nil
}

// Put 归还缓冲区到池中
func (bp *BufferPool) Put(buffer []byte) {
	if buffer == nil {
		return
	}

	size := cap(buffer) // 使用容量而不是长度

	bp.mu.RLock()
	pool, exists := bp.pools[size]
	bp.mu.RUnlock()

	if exists {
		// 清零缓冲区（可选，用于安全性）
		for i := range buffer {
			buffer[i] = 0
		}

		pool.Put(buffer[:cap(buffer)]) // 恢复原始大小
		atomic.AddInt64(&bp.metrics.CurrentInUse, -1)
		atomic.AddInt64(&bp.metrics.TotalReleased, 1)

		log.Debugf("Returned buffer of size %d to pool", size)
	} else {
		// 如果没有对应的池，直接释放内存
		atomic.AddInt64(&bp.currentMem, -int64(size))
		atomic.AddInt64(&bp.metrics.CurrentInUse, -1)
		log.Debugf("Released buffer of size %d (no pool)", size)
	}
}

// findBestSize 找到最适合的缓冲区大小
func (bp *BufferPool) findBestSize(requestedSize int) int {
	for _, size := range bp.sizes {
		if size >= requestedSize {
			return size
		}
	}
	// 如果请求的大小超过所有预定义大小，返回最大的
	return bp.sizes[len(bp.sizes)-1]
}

// GetMetrics 获取池指标
func (bp *BufferPool) GetMetrics() *PoolMetrics {
	bp.metrics.mu.RLock()
	defer bp.metrics.mu.RUnlock()
	
	// 创建指标副本
	metrics := &PoolMetrics{
		TotalAllocated:   atomic.LoadInt64(&bp.metrics.TotalAllocated),
		TotalReleased:    atomic.LoadInt64(&bp.metrics.TotalReleased),
		CurrentInUse:     atomic.LoadInt64(&bp.metrics.CurrentInUse),
		PeakMemoryUsage:  atomic.LoadInt64(&bp.metrics.PeakMemoryUsage),
		SizeDistribution: make(map[int]int64),
	}
	
	// 复制大小分布
	for size, count := range bp.metrics.SizeDistribution {
		metrics.SizeDistribution[size] = count
	}
	
	// 计算命中率
	total := metrics.TotalAllocated
	if total > 0 {
		metrics.PoolHitRate = float64(metrics.TotalReleased) / float64(total) * 100
	}
	
	return metrics
}

// GetCurrentMemoryUsage 获取当前内存使用量
func (bp *BufferPool) GetCurrentMemoryUsage() int64 {
	return atomic.LoadInt64(&bp.currentMem)
}

// GetMaxMemoryUsage 获取最大内存限制
func (bp *BufferPool) GetMaxMemoryUsage() int64 {
	return bp.maxMemUsage
}

// startMemoryMonitor 启动内存监控协程
func (bp *BufferPool) startMemoryMonitor() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	
	for range ticker.C {
		current := atomic.LoadInt64(&bp.currentMem)
		inUse := atomic.LoadInt64(&bp.metrics.CurrentInUse)
		
		// 记录内存使用情况
		log.Debugf("Buffer pool memory usage: %d bytes (%d MB), buffers in use: %d", 
			current, current/(1024*1024), inUse)
		
		// 如果内存使用过高，触发GC
		if float64(current) > float64(bp.maxMemUsage)*0.8 {
			log.Warnf("High memory usage detected: %d/%d bytes (%.1f%%), triggering GC", 
				current, bp.maxMemUsage, float64(current)/float64(bp.maxMemUsage)*100)
			runtime.GC()
		}
	}
}

// Cleanup 清理缓冲区池
func (bp *BufferPool) Cleanup() {
	bp.mu.Lock()
	defer bp.mu.Unlock()
	
	// 清空所有池
	for size := range bp.pools {
		bp.pools[size] = &sync.Pool{
			New: func() interface{} {
				return make([]byte, size)
			},
		}
	}
	
	// 重置内存计数
	atomic.StoreInt64(&bp.currentMem, 0)
	atomic.StoreInt64(&bp.metrics.CurrentInUse, 0)
	
	log.Info("Buffer pool cleaned up")
}

// 全局缓冲区池实例
var GlobalBufferPool *BufferPool

// InitBufferPool 初始化全局缓冲区池
func InitBufferPool(maxMemoryMB int64) {
	GlobalBufferPool = NewBufferPool(maxMemoryMB)
}
