package memory

import (
	"sync"
	"testing"
	"time"
)

func TestNewBufferPool(t *testing.T) {
	maxMemoryMB := int64(10)
	bp := NewBufferPool(maxMemoryMB)
	
	if bp == nil {
		t.Fatal("NewBufferPool returned nil")
	}
	
	if bp.GetMaxMemoryUsage() != maxMemoryMB*1024*1024 {
		t.<PERSON><PERSON>("Expected max memory %d, got %d", maxMemoryMB*1024*1024, bp.GetMaxMemoryUsage())
	}
	
	if len(bp.pools) != len(defaultSizes) {
		t.<PERSON>rrorf("Expected %d pools, got %d", len(defaultSizes), len(bp.pools))
	}
}

func TestBufferPoolGetPut(t *testing.T) {
	bp := NewBufferPool(10) // 10MB
	
	// 测试获取缓冲区
	buffer, err := bp.Get(1024)
	if err != nil {
		t.Fatalf("Failed to get buffer: %v", err)
	}
	
	if len(buffer) != 1024 {
		t.<PERSON><PERSON><PERSON>("Expected buffer size 1024, got %d", len(buffer))
	}
	
	// 检查指标
	metrics := bp.GetMetrics()
	if metrics.CurrentInUse != 1 {
		t.Errorf("Expected 1 buffer in use, got %d", metrics.CurrentInUse)
	}
	
	// 测试归还缓冲区
	bp.Put(buffer)
	
	// 检查指标
	metrics = bp.GetMetrics()
	if metrics.CurrentInUse != 0 {
		t.Errorf("Expected 0 buffers in use, got %d", metrics.CurrentInUse)
	}
	
	if metrics.TotalReleased != 1 {
		t.Errorf("Expected 1 buffer released, got %d", metrics.TotalReleased)
	}
}

func TestBufferPoolSizeSelection(t *testing.T) {
	bp := NewBufferPool(10)
	
	testCases := []struct {
		requestSize  int
		expectedSize int
	}{
		{500, 1024},      // 应该选择1KB
		{2000, 4096},     // 应该选择4KB
		{10000, 16384},   // 应该选择16KB
		{100000, 262144}, // 应该选择256KB
	}
	
	for _, tc := range testCases {
		buffer, err := bp.Get(tc.requestSize)
		if err != nil {
			t.Fatalf("Failed to get buffer for size %d: %v", tc.requestSize, err)
		}
		
		if cap(buffer) != tc.expectedSize {
			t.Errorf("For request size %d, expected capacity %d, got %d", 
				tc.requestSize, tc.expectedSize, cap(buffer))
		}
		
		bp.Put(buffer)
	}
}

func TestBufferPoolConcurrency(t *testing.T) {
	bp := NewBufferPool(50) // 50MB
	numGoroutines := 100
	buffersPerGoroutine := 10
	
	var wg sync.WaitGroup
	errors := make(chan error, numGoroutines)
	
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			buffers := make([][]byte, buffersPerGoroutine)
			
			// 获取缓冲区
			for j := 0; j < buffersPerGoroutine; j++ {
				buffer, err := bp.Get(1024)
				if err != nil {
					errors <- err
					return
				}
				buffers[j] = buffer
			}
			
			// 模拟使用
			time.Sleep(10 * time.Millisecond)
			
			// 归还缓冲区
			for _, buffer := range buffers {
				bp.Put(buffer)
			}
		}(i)
	}
	
	wg.Wait()
	close(errors)
	
	// 检查错误
	for err := range errors {
		t.Errorf("Concurrency test error: %v", err)
	}
	
	// 检查最终状态
	metrics := bp.GetMetrics()
	if metrics.CurrentInUse != 0 {
		t.Errorf("Expected 0 buffers in use after test, got %d", metrics.CurrentInUse)
	}
	
	expectedTotal := int64(numGoroutines * buffersPerGoroutine)
	if metrics.TotalReleased != expectedTotal {
		t.Errorf("Expected %d buffers released, got %d", expectedTotal, metrics.TotalReleased)
	}
}

func TestBufferPoolMemoryLimit(t *testing.T) {
	bp := NewBufferPool(5) // 5MB限制

	// 第一阶段：获取缓冲区直到达到限制
	var buffers [][]byte
	var lastErr error

	for i := 0; i < 10; i++ {
		buffer, err := bp.Get(1024 * 1024) // 1MB缓冲区
		if err != nil {
			lastErr = err
			break
		}
		buffers = append(buffers, buffer)
	}

	// 应该在某个点触发内存限制
	if lastErr == nil {
		t.Error("Expected memory limit error, but got none")
	}

	// 检查我们至少获取了一些缓冲区
	if len(buffers) == 0 {
		t.Error("Expected to get at least one buffer before hitting limit")
	}

	t.Logf("Successfully allocated %d buffers before hitting limit", len(buffers))

	// 第二阶段：归还缓冲区并测试重用
	for _, buffer := range buffers {
		bp.Put(buffer)
	}

	// 第三阶段：测试重用 - 应该能够获取相同数量的缓冲区
	var reusedBuffers [][]byte
	for i := 0; i < len(buffers); i++ {
		buffer, err := bp.Get(1024 * 1024)
		if err != nil {
			t.Errorf("Failed to reuse buffer %d: %v", i, err)
			break
		}
		reusedBuffers = append(reusedBuffers, buffer)
	}

	if len(reusedBuffers) != len(buffers) {
		t.Errorf("Expected to reuse %d buffers, but got %d", len(buffers), len(reusedBuffers))
	}

	// 清理
	for _, buffer := range reusedBuffers {
		bp.Put(buffer)
	}

	t.Log("Memory limit test completed successfully")
}

func TestBufferPoolMetrics(t *testing.T) {
	bp := NewBufferPool(10)
	
	// 获取一些缓冲区
	buffers := make([][]byte, 5)
	for i := 0; i < 5; i++ {
		buffer, err := bp.Get(1024)
		if err != nil {
			t.Fatalf("Failed to get buffer %d: %v", i, err)
		}
		buffers[i] = buffer
	}
	
	metrics := bp.GetMetrics()
	
	// 检查基本指标
	if metrics.CurrentInUse != 5 {
		t.Errorf("Expected 5 buffers in use, got %d", metrics.CurrentInUse)
	}
	
	if metrics.TotalAllocated < 5 {
		t.Errorf("Expected at least 5 allocations, got %d", metrics.TotalAllocated)
	}
	
	// 检查大小分布
	if metrics.SizeDistribution[1024] == 0 {
		t.Error("Expected non-zero count for 1024 byte buffers")
	}
	
	// 归还缓冲区
	for _, buffer := range buffers {
		bp.Put(buffer)
	}
	
	finalMetrics := bp.GetMetrics()
	if finalMetrics.CurrentInUse != 0 {
		t.Errorf("Expected 0 buffers in use after cleanup, got %d", finalMetrics.CurrentInUse)
	}
	
	if finalMetrics.TotalReleased != 5 {
		t.Errorf("Expected 5 buffers released, got %d", finalMetrics.TotalReleased)
	}
}

func TestBufferPoolCleanup(t *testing.T) {
	bp := NewBufferPool(10)
	
	// 获取一些缓冲区
	for i := 0; i < 5; i++ {
		buffer, err := bp.Get(1024)
		if err != nil {
			t.Fatalf("Failed to get buffer %d: %v", i, err)
		}
		// 故意不归还，模拟内存泄漏
		_ = buffer
	}
	
	initialMemory := bp.GetCurrentMemoryUsage()
	if initialMemory == 0 {
		t.Error("Expected non-zero memory usage before cleanup")
	}
	
	// 执行清理
	bp.Cleanup()
	
	// 检查清理后的状态
	metrics := bp.GetMetrics()
	if metrics.CurrentInUse != 0 {
		t.Errorf("Expected 0 buffers in use after cleanup, got %d", metrics.CurrentInUse)
	}
	
	finalMemory := bp.GetCurrentMemoryUsage()
	if finalMemory != 0 {
		t.Errorf("Expected 0 memory usage after cleanup, got %d", finalMemory)
	}
}

// 性能测试
func BenchmarkBufferPoolGet(b *testing.B) {
	bp := NewBufferPool(100) // 100MB
	
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			buffer, err := bp.Get(1024)
			if err != nil {
				b.Fatalf("Failed to get buffer: %v", err)
			}
			bp.Put(buffer)
		}
	})
}

func BenchmarkBufferPoolGetLarge(b *testing.B) {
	bp := NewBufferPool(500) // 500MB
	
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			buffer, err := bp.Get(1024 * 1024) // 1MB
			if err != nil {
				b.Fatalf("Failed to get buffer: %v", err)
			}
			bp.Put(buffer)
		}
	})
}
