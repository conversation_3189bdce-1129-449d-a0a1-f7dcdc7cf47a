package resume

import (
	"crypto/md5"
	"fmt"
	"io"
	"time"

	"github.com/google/uuid"
	log "github.com/sirupsen/logrus"
)

// UploadProgressManager 上传进度管理器
type UploadProgressManager struct {
	storage ResumeStorage
}

// NewUploadProgressManager 创建上传进度管理器
func NewUploadProgressManager(storage ResumeStorage) *UploadProgressManager {
	return &UploadProgressManager{
		storage: storage,
	}
}

// CreateUploadSession 创建上传会话
func (upm *UploadProgressManager) CreateUploadSession(userID, fileName, filePath string, 
	fileSize, chunkSize int64, metadata map[string]string) (*UploadSession, error) {
	
	// 计算分片数量
	totalChunks := (fileSize + chunkSize - 1) / chunkSize
	
	// 创建会话
	session := &UploadSession{
		ID:              uuid.New().String(),
		UserID:          userID,
		FileName:        fileName,
		FilePath:        filePath,
		FileSize:        fileSize,
		ChunkSize:       chunkSize,
		TotalChunks:     totalChunks,
		CompletedChunks: make(map[int64]bool),
		UploadedBytes:   0,
		ExpiresAt:       time.Now().Add(24 * time.Hour), // 24小时过期
		Metadata:        metadata,
		ChunkChecksums:  make(map[int64]string),
	}
	
	if err := upm.storage.CreateSession(session); err != nil {
		return nil, fmt.Errorf("failed to create session: %w", err)
	}
	
	log.Infof("Created upload session %s for file %s (%d bytes, %d chunks)", 
		session.ID, fileName, fileSize, totalChunks)
	
	return session, nil
}

// ResumeUploadSession 恢复上传会话
func (upm *UploadProgressManager) ResumeUploadSession(sessionID string) (*UploadSession, error) {
	session, err := upm.storage.GetSession(sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get session: %w", err)
	}
	
	// 检查会话状态
	if session.Status == StatusCompleted {
		return nil, fmt.Errorf("session %s is already completed", sessionID)
	}
	
	if session.Status == StatusFailed || session.Status == StatusCancelled {
		return nil, fmt.Errorf("session %s is in invalid state: %s", sessionID, session.Status)
	}
	
	log.Infof("Resumed upload session %s, progress: %d/%d bytes", 
		sessionID, session.UploadedBytes, session.FileSize)
	
	return session, nil
}

// MarkChunkCompleted 标记分片完成
func (upm *UploadProgressManager) MarkChunkCompleted(sessionID string, chunkIndex int64, 
	chunkData []byte) error {
	
	// 计算分片校验和
	checksum := fmt.Sprintf("%x", md5.Sum(chunkData))
	
	if err := upm.storage.MarkChunkCompleted(sessionID, chunkIndex, checksum); err != nil {
		return fmt.Errorf("failed to mark chunk completed: %w", err)
	}
	
	// 获取更新后的会话
	session, err := upm.storage.GetSession(sessionID)
	if err != nil {
		return fmt.Errorf("failed to get updated session: %w", err)
	}
	
	// 检查是否完成
	if len(session.CompletedChunks) >= int(session.TotalChunks) {
		session.Status = StatusCompleted
		if err := upm.storage.UpdateSession(session); err != nil {
			log.Errorf("Failed to update session status to completed: %v", err)
		} else {
			log.Infof("Upload session %s completed successfully", sessionID)
		}
	}
	
	return nil
}

// GetUploadProgress 获取上传进度
func (upm *UploadProgressManager) GetUploadProgress(sessionID string) (*UploadProgress, error) {
	session, err := upm.storage.GetSession(sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get session: %w", err)
	}
	
	progress := &UploadProgress{
		SessionID:       session.ID,
		FileName:        session.FileName,
		FileSize:        session.FileSize,
		UploadedBytes:   session.UploadedBytes,
		TotalChunks:     session.TotalChunks,
		CompletedChunks: int64(len(session.CompletedChunks)),
		Status:          session.Status,
		CreatedAt:       session.CreatedAt,
		UpdatedAt:       session.UpdatedAt,
		ExpiresAt:       session.ExpiresAt,
	}
	
	// 计算进度百分比
	if session.FileSize > 0 {
		progress.ProgressPercent = float64(session.UploadedBytes) / float64(session.FileSize) * 100
	}
	
	// 计算预估剩余时间
	if progress.ProgressPercent > 0 {
		elapsed := time.Since(session.CreatedAt)
		totalEstimated := time.Duration(float64(elapsed) / (progress.ProgressPercent / 100))
		progress.EstimatedTimeRemaining = totalEstimated - elapsed
		
		if progress.EstimatedTimeRemaining < 0 {
			progress.EstimatedTimeRemaining = 0
		}
	}
	
	return progress, nil
}

// GetMissingChunks 获取缺失的分片列表
func (upm *UploadProgressManager) GetMissingChunks(sessionID string) ([]int64, error) {
	session, err := upm.storage.GetSession(sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get session: %w", err)
	}
	
	var missingChunks []int64
	for i := int64(0); i < session.TotalChunks; i++ {
		if !session.CompletedChunks[i] {
			missingChunks = append(missingChunks, i)
		}
	}
	
	return missingChunks, nil
}

// ValidateChunk 验证分片数据
func (upm *UploadProgressManager) ValidateChunk(sessionID string, chunkIndex int64, 
	chunkData []byte) error {
	
	session, err := upm.storage.GetSession(sessionID)
	if err != nil {
		return fmt.Errorf("failed to get session: %w", err)
	}
	
	// 检查分片索引
	if chunkIndex < 0 || chunkIndex >= session.TotalChunks {
		return fmt.Errorf("invalid chunk index %d, expected 0-%d", chunkIndex, session.TotalChunks-1)
	}
	
	// 检查分片大小
	expectedSize := session.ChunkSize
	if chunkIndex == session.TotalChunks-1 {
		// 最后一个分片可能较小
		remainingBytes := session.FileSize % session.ChunkSize
		if remainingBytes > 0 {
			expectedSize = remainingBytes
		}
	}
	
	if int64(len(chunkData)) != expectedSize {
		return fmt.Errorf("invalid chunk size %d, expected %d", len(chunkData), expectedSize)
	}
	
	// 如果分片已经存在，验证校验和
	if existingChecksum, exists := session.ChunkChecksums[chunkIndex]; exists {
		currentChecksum := fmt.Sprintf("%x", md5.Sum(chunkData))
		if currentChecksum != existingChecksum {
			return fmt.Errorf("chunk checksum mismatch: expected %s, got %s", 
				existingChecksum, currentChecksum)
		}
	}
	
	return nil
}

// CancelUploadSession 取消上传会话
func (upm *UploadProgressManager) CancelUploadSession(sessionID string) error {
	session, err := upm.storage.GetSession(sessionID)
	if err != nil {
		return fmt.Errorf("failed to get session: %w", err)
	}
	
	session.Status = StatusCancelled
	session.UpdatedAt = time.Now()
	
	if err := upm.storage.UpdateSession(session); err != nil {
		return fmt.Errorf("failed to update session: %w", err)
	}
	
	log.Infof("Cancelled upload session %s", sessionID)
	return nil
}

// DeleteUploadSession 删除上传会话
func (upm *UploadProgressManager) DeleteUploadSession(sessionID string) error {
	if err := upm.storage.DeleteSession(sessionID); err != nil {
		return fmt.Errorf("failed to delete session: %w", err)
	}
	
	log.Infof("Deleted upload session %s", sessionID)
	return nil
}

// ListUserSessions 列出用户的上传会话
func (upm *UploadProgressManager) ListUserSessions(userID string) ([]*UploadProgress, error) {
	sessions, err := upm.storage.ListSessions(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to list sessions: %w", err)
	}
	
	var progressList []*UploadProgress
	for _, session := range sessions {
		progress, err := upm.GetUploadProgress(session.ID)
		if err != nil {
			log.Errorf("Failed to get progress for session %s: %v", session.ID, err)
			continue
		}
		progressList = append(progressList, progress)
	}
	
	return progressList, nil
}

// UploadProgress 上传进度信息
type UploadProgress struct {
	SessionID              string        `json:"session_id"`
	FileName               string        `json:"file_name"`
	FileSize               int64         `json:"file_size"`
	UploadedBytes          int64         `json:"uploaded_bytes"`
	TotalChunks            int64         `json:"total_chunks"`
	CompletedChunks        int64         `json:"completed_chunks"`
	ProgressPercent        float64       `json:"progress_percent"`
	Status                 SessionStatus `json:"status"`
	CreatedAt              time.Time     `json:"created_at"`
	UpdatedAt              time.Time     `json:"updated_at"`
	ExpiresAt              time.Time     `json:"expires_at"`
	EstimatedTimeRemaining time.Duration `json:"estimated_time_remaining"`
}

// CalculateFileChecksum 计算文件校验和
func CalculateFileChecksum(reader io.Reader) (string, error) {
	hash := md5.New()
	if _, err := io.Copy(hash, reader); err != nil {
		return "", err
	}
	return fmt.Sprintf("%x", hash.Sum(nil)), nil
}

// 全局进度管理器
var GlobalProgressManager *UploadProgressManager

// InitProgressManager 初始化进度管理器
func InitProgressManager() {
	if GlobalResumeStorage == nil {
		InitResumeStorage()
	}
	GlobalProgressManager = NewUploadProgressManager(GlobalResumeStorage)
	log.Info("Upload progress manager initialized")
}
