package resume

import (
	"testing"
	"time"
)

func TestUploadProgressManager_CreateUploadSession(t *testing.T) {
	storage := NewMemoryResumeStorage()
	manager := NewUploadProgressManager(storage)
	
	metadata := map[string]string{
		"content_type": "text/plain",
		"upload_type":  "resumable",
	}
	
	session, err := manager.CreateUploadSession("user1", "test.txt", "/uploads/test.txt", 
		1024, 256, metadata)
	if err != nil {
		t.Fatalf("Failed to create upload session: %v", err)
	}
	
	if session.UserID != "user1" {
		t.<PERSON><PERSON><PERSON>("Expected user ID 'user1', got '%s'", session.UserID)
	}
	
	if session.FileName != "test.txt" {
		t.<PERSON><PERSON>rf("Expected file name 'test.txt', got '%s'", session.FileName)
	}
	
	if session.FileSize != 1024 {
		t.<PERSON><PERSON><PERSON>("Expected file size 1024, got %d", session.FileSize)
	}
	
	if session.ChunkSize != 256 {
		t.<PERSON>rrorf("Expected chunk size 256, got %d", session.ChunkSize)
	}
	
	if session.TotalChunks != 4 {
		t.<PERSON><PERSON><PERSON>("Expected 4 total chunks, got %d", session.TotalChunks)
	}
	
	if session.Status != StatusActive {
		t.Errorf("Expected status %s, got %s", StatusActive, session.Status)
	}
	
	if session.Metadata["content_type"] != "text/plain" {
		t.Errorf("Expected content_type 'text/plain', got '%s'", session.Metadata["content_type"])
	}
}

func TestUploadProgressManager_ResumeUploadSession(t *testing.T) {
	storage := NewMemoryResumeStorage()
	manager := NewUploadProgressManager(storage)
	
	// 创建会话
	session, err := manager.CreateUploadSession("user1", "test.txt", "/uploads/test.txt", 
		1024, 256, nil)
	if err != nil {
		t.Fatalf("Failed to create upload session: %v", err)
	}
	
	// 恢复会话
	resumedSession, err := manager.ResumeUploadSession(session.ID)
	if err != nil {
		t.Fatalf("Failed to resume upload session: %v", err)
	}
	
	if resumedSession.ID != session.ID {
		t.Errorf("Expected session ID %s, got %s", session.ID, resumedSession.ID)
	}
	
	if resumedSession.Status != StatusActive {
		t.Errorf("Expected status %s, got %s", StatusActive, resumedSession.Status)
	}
}

func TestUploadProgressManager_ResumeCompletedSession(t *testing.T) {
	storage := NewMemoryResumeStorage()
	manager := NewUploadProgressManager(storage)
	
	// 创建已完成的会话
	session := &UploadSession{
		ID:       "completed-session",
		UserID:   "user1",
		FileName: "test.txt",
		FileSize: 1024,
		Status:   StatusCompleted,
		ExpiresAt: time.Now().Add(time.Hour),
	}
	
	err := storage.CreateSession(session)
	if err != nil {
		t.Fatalf("Failed to create session: %v", err)
	}
	
	// 尝试恢复已完成的会话
	_, err = manager.ResumeUploadSession("completed-session")
	if err == nil {
		t.Error("Expected error when resuming completed session, got nil")
	}
}

func TestUploadProgressManager_MarkChunkCompleted(t *testing.T) {
	storage := NewMemoryResumeStorage()
	manager := NewUploadProgressManager(storage)
	
	session, err := manager.CreateUploadSession("user1", "test.txt", "/uploads/test.txt", 
		1024, 256, nil)
	if err != nil {
		t.Fatalf("Failed to create upload session: %v", err)
	}
	
	// 标记分片完成
	chunkData := make([]byte, 256)
	for i := range chunkData {
		chunkData[i] = byte(i % 256)
	}
	
	err = manager.MarkChunkCompleted(session.ID, 0, chunkData)
	if err != nil {
		t.Fatalf("Failed to mark chunk completed: %v", err)
	}
	
	err = manager.MarkChunkCompleted(session.ID, 2, chunkData)
	if err != nil {
		t.Fatalf("Failed to mark chunk completed: %v", err)
	}
	
	// 获取进度
	progress, err := manager.GetUploadProgress(session.ID)
	if err != nil {
		t.Fatalf("Failed to get upload progress: %v", err)
	}
	
	if progress.CompletedChunks != 2 {
		t.Errorf("Expected 2 completed chunks, got %d", progress.CompletedChunks)
	}
	
	expectedBytes := int64(2) * 256
	if progress.UploadedBytes != expectedBytes {
		t.Errorf("Expected uploaded bytes %d, got %d", expectedBytes, progress.UploadedBytes)
	}
	
	expectedPercent := float64(expectedBytes) / float64(session.FileSize) * 100
	if progress.ProgressPercent != expectedPercent {
		t.Errorf("Expected progress percent %.2f, got %.2f", expectedPercent, progress.ProgressPercent)
	}
}

func TestUploadProgressManager_GetMissingChunks(t *testing.T) {
	storage := NewMemoryResumeStorage()
	manager := NewUploadProgressManager(storage)
	
	session, err := manager.CreateUploadSession("user1", "test.txt", "/uploads/test.txt", 
		1024, 256, nil)
	if err != nil {
		t.Fatalf("Failed to create upload session: %v", err)
	}
	
	// 标记一些分片完成
	chunkData := make([]byte, 256)
	manager.MarkChunkCompleted(session.ID, 0, chunkData)
	manager.MarkChunkCompleted(session.ID, 2, chunkData)
	
	// 获取缺失的分片
	missingChunks, err := manager.GetMissingChunks(session.ID)
	if err != nil {
		t.Fatalf("Failed to get missing chunks: %v", err)
	}
	
	expectedMissing := []int64{1, 3}
	if len(missingChunks) != len(expectedMissing) {
		t.Errorf("Expected %d missing chunks, got %d", len(expectedMissing), len(missingChunks))
	}
	
	for i, expected := range expectedMissing {
		if missingChunks[i] != expected {
			t.Errorf("Expected missing chunk %d, got %d", expected, missingChunks[i])
		}
	}
}

func TestUploadProgressManager_ValidateChunk(t *testing.T) {
	storage := NewMemoryResumeStorage()
	manager := NewUploadProgressManager(storage)
	
	session, err := manager.CreateUploadSession("user1", "test.txt", "/uploads/test.txt", 
		1024, 256, nil)
	if err != nil {
		t.Fatalf("Failed to create upload session: %v", err)
	}
	
	// 测试有效分片
	validChunk := make([]byte, 256)
	err = manager.ValidateChunk(session.ID, 0, validChunk)
	if err != nil {
		t.Errorf("Expected valid chunk to pass validation, got error: %v", err)
	}
	
	// 测试最后一个分片（可能较小）
	lastChunkSize := session.FileSize % session.ChunkSize
	if lastChunkSize == 0 {
		lastChunkSize = session.ChunkSize
	}
	lastChunk := make([]byte, lastChunkSize)
	err = manager.ValidateChunk(session.ID, session.TotalChunks-1, lastChunk)
	if err != nil {
		t.Errorf("Expected valid last chunk to pass validation, got error: %v", err)
	}
	
	// 测试无效分片索引
	err = manager.ValidateChunk(session.ID, -1, validChunk)
	if err == nil {
		t.Error("Expected error for invalid chunk index, got nil")
	}
	
	err = manager.ValidateChunk(session.ID, session.TotalChunks, validChunk)
	if err == nil {
		t.Error("Expected error for chunk index out of range, got nil")
	}
	
	// 测试无效分片大小
	invalidChunk := make([]byte, 100)
	err = manager.ValidateChunk(session.ID, 0, invalidChunk)
	if err == nil {
		t.Error("Expected error for invalid chunk size, got nil")
	}
}

func TestUploadProgressManager_CancelUploadSession(t *testing.T) {
	storage := NewMemoryResumeStorage()
	manager := NewUploadProgressManager(storage)
	
	session, err := manager.CreateUploadSession("user1", "test.txt", "/uploads/test.txt", 
		1024, 256, nil)
	if err != nil {
		t.Fatalf("Failed to create upload session: %v", err)
	}
	
	// 取消会话
	err = manager.CancelUploadSession(session.ID)
	if err != nil {
		t.Fatalf("Failed to cancel upload session: %v", err)
	}
	
	// 验证会话状态
	cancelledSession, err := storage.GetSession(session.ID)
	if err != nil {
		t.Fatalf("Failed to get cancelled session: %v", err)
	}
	
	if cancelledSession.Status != StatusCancelled {
		t.Errorf("Expected status %s, got %s", StatusCancelled, cancelledSession.Status)
	}
}

func TestUploadProgressManager_ListUserSessions(t *testing.T) {
	storage := NewMemoryResumeStorage()
	manager := NewUploadProgressManager(storage)
	
	// 创建多个会话
	session1, _ := manager.CreateUploadSession("user1", "file1.txt", "/uploads/file1.txt",
		1024, 256, nil)
	_, _ = manager.CreateUploadSession("user1", "file2.txt", "/uploads/file2.txt",
		2048, 512, nil)
	_, _ = manager.CreateUploadSession("user2", "file3.txt", "/uploads/file3.txt",
		512, 128, nil)
	
	// 标记一些进度
	chunkData := make([]byte, 256)
	manager.MarkChunkCompleted(session1.ID, 0, chunkData)
	
	// 获取user1的会话
	user1Sessions, err := manager.ListUserSessions("user1")
	if err != nil {
		t.Fatalf("Failed to list user sessions: %v", err)
	}
	
	if len(user1Sessions) != 2 {
		t.Errorf("Expected 2 sessions for user1, got %d", len(user1Sessions))
	}
	
	// 验证进度信息
	var session1Progress *UploadProgress
	for _, progress := range user1Sessions {
		if progress.SessionID == session1.ID {
			session1Progress = progress
			break
		}
	}
	
	if session1Progress == nil {
		t.Error("Expected to find session1 progress")
	} else {
		if session1Progress.CompletedChunks != 1 {
			t.Errorf("Expected 1 completed chunk for session1, got %d", session1Progress.CompletedChunks)
		}
	}
	
	// 获取user2的会话
	user2Sessions, err := manager.ListUserSessions("user2")
	if err != nil {
		t.Fatalf("Failed to list user sessions: %v", err)
	}
	
	if len(user2Sessions) != 1 {
		t.Errorf("Expected 1 session for user2, got %d", len(user2Sessions))
	}
}

func TestUploadProgressManager_CompleteUpload(t *testing.T) {
	storage := NewMemoryResumeStorage()
	manager := NewUploadProgressManager(storage)
	
	session, err := manager.CreateUploadSession("user1", "test.txt", "/uploads/test.txt", 
		1024, 256, nil)
	if err != nil {
		t.Fatalf("Failed to create upload session: %v", err)
	}
	
	// 完成所有分片
	chunkData := make([]byte, 256)
	for i := int64(0); i < session.TotalChunks; i++ {
		err = manager.MarkChunkCompleted(session.ID, i, chunkData)
		if err != nil {
			t.Fatalf("Failed to mark chunk %d completed: %v", i, err)
		}
	}
	
	// 验证会话已完成
	completedSession, err := storage.GetSession(session.ID)
	if err != nil {
		t.Fatalf("Failed to get completed session: %v", err)
	}
	
	if completedSession.Status != StatusCompleted {
		t.Errorf("Expected status %s, got %s", StatusCompleted, completedSession.Status)
	}
	
	// 获取进度
	progress, err := manager.GetUploadProgress(session.ID)
	if err != nil {
		t.Fatalf("Failed to get upload progress: %v", err)
	}
	
	if progress.ProgressPercent != 100.0 {
		t.Errorf("Expected progress percent 100.0, got %.2f", progress.ProgressPercent)
	}
}
