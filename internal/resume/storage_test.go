package resume

import (
	"testing"
	"time"
)

func TestMemoryResumeStorage_CreateSession(t *testing.T) {
	storage := NewMemoryResumeStorage()
	
	session := &UploadSession{
		ID:       "test-session-1",
		UserID:   "user1",
		FileName: "test.txt",
		FileSize: 1024,
		ChunkSize: 256,
		TotalChunks: 4,
		ExpiresAt: time.Now().Add(time.Hour),
	}
	
	err := storage.CreateSession(session)
	if err != nil {
		t.Fatalf("Failed to create session: %v", err)
	}
	
	// 验证会话已创建
	retrieved, err := storage.GetSession("test-session-1")
	if err != nil {
		t.Fatalf("Failed to get session: %v", err)
	}
	
	if retrieved.ID != session.ID {
		t.Errorf("Expected session ID %s, got %s", session.ID, retrieved.ID)
	}
	
	if retrieved.Status != StatusActive {
		t.Errorf("Expected status %s, got %s", StatusActive, retrieved.Status)
	}
}

func TestMemoryResumeStorage_DuplicateSession(t *testing.T) {
	storage := NewMemoryResumeStorage()
	
	session := &UploadSession{
		ID:       "test-session-1",
		UserID:   "user1",
		FileName: "test.txt",
		FileSize: 1024,
		ExpiresAt: time.Now().Add(time.Hour),
	}
	
	// 第一次创建应该成功
	err := storage.CreateSession(session)
	if err != nil {
		t.Fatalf("Failed to create session: %v", err)
	}
	
	// 第二次创建应该失败
	err = storage.CreateSession(session)
	if err == nil {
		t.Error("Expected error for duplicate session, got nil")
	}
}

func TestMemoryResumeStorage_UpdateSession(t *testing.T) {
	storage := NewMemoryResumeStorage()
	
	session := &UploadSession{
		ID:       "test-session-1",
		UserID:   "user1",
		FileName: "test.txt",
		FileSize: 1024,
		UploadedBytes: 0,
		ExpiresAt: time.Now().Add(time.Hour),
	}
	
	// 创建会话
	err := storage.CreateSession(session)
	if err != nil {
		t.Fatalf("Failed to create session: %v", err)
	}
	
	// 更新会话
	session.UploadedBytes = 512
	err = storage.UpdateSession(session)
	if err != nil {
		t.Fatalf("Failed to update session: %v", err)
	}
	
	// 验证更新
	retrieved, err := storage.GetSession("test-session-1")
	if err != nil {
		t.Fatalf("Failed to get session: %v", err)
	}
	
	if retrieved.UploadedBytes != 512 {
		t.Errorf("Expected uploaded bytes 512, got %d", retrieved.UploadedBytes)
	}
}

func TestMemoryResumeStorage_MarkChunkCompleted(t *testing.T) {
	storage := NewMemoryResumeStorage()
	
	session := &UploadSession{
		ID:        "test-session-1",
		UserID:    "user1",
		FileName:  "test.txt",
		FileSize:  1024,
		ChunkSize: 256,
		TotalChunks: 4,
		ExpiresAt: time.Now().Add(time.Hour),
	}
	
	err := storage.CreateSession(session)
	if err != nil {
		t.Fatalf("Failed to create session: %v", err)
	}
	
	// 标记分片完成
	err = storage.MarkChunkCompleted("test-session-1", 0, "checksum1")
	if err != nil {
		t.Fatalf("Failed to mark chunk completed: %v", err)
	}
	
	err = storage.MarkChunkCompleted("test-session-1", 2, "checksum3")
	if err != nil {
		t.Fatalf("Failed to mark chunk completed: %v", err)
	}
	
	// 验证分片状态
	chunks, err := storage.GetCompletedChunks("test-session-1")
	if err != nil {
		t.Fatalf("Failed to get completed chunks: %v", err)
	}
	
	if !chunks[0] {
		t.Error("Expected chunk 0 to be completed")
	}
	
	if chunks[1] {
		t.Error("Expected chunk 1 to be incomplete")
	}
	
	if !chunks[2] {
		t.Error("Expected chunk 2 to be completed")
	}
	
	// 验证上传字节数更新
	retrieved, err := storage.GetSession("test-session-1")
	if err != nil {
		t.Fatalf("Failed to get session: %v", err)
	}
	
	expectedBytes := int64(2) * session.ChunkSize // 2个分片完成
	if retrieved.UploadedBytes != expectedBytes {
		t.Errorf("Expected uploaded bytes %d, got %d", expectedBytes, retrieved.UploadedBytes)
	}
}

func TestMemoryResumeStorage_ListSessions(t *testing.T) {
	storage := NewMemoryResumeStorage()
	
	// 创建多个会话
	sessions := []*UploadSession{
		{
			ID:       "session-1",
			UserID:   "user1",
			FileName: "file1.txt",
			FileSize: 1024,
			ExpiresAt: time.Now().Add(time.Hour),
		},
		{
			ID:       "session-2",
			UserID:   "user1",
			FileName: "file2.txt",
			FileSize: 2048,
			ExpiresAt: time.Now().Add(time.Hour),
		},
		{
			ID:       "session-3",
			UserID:   "user2",
			FileName: "file3.txt",
			FileSize: 512,
			ExpiresAt: time.Now().Add(time.Hour),
		},
	}
	
	for _, session := range sessions {
		err := storage.CreateSession(session)
		if err != nil {
			t.Fatalf("Failed to create session %s: %v", session.ID, err)
		}
	}
	
	// 获取user1的会话
	user1Sessions, err := storage.ListSessions("user1")
	if err != nil {
		t.Fatalf("Failed to list sessions for user1: %v", err)
	}
	
	if len(user1Sessions) != 2 {
		t.Errorf("Expected 2 sessions for user1, got %d", len(user1Sessions))
	}
	
	// 获取user2的会话
	user2Sessions, err := storage.ListSessions("user2")
	if err != nil {
		t.Fatalf("Failed to list sessions for user2: %v", err)
	}
	
	if len(user2Sessions) != 1 {
		t.Errorf("Expected 1 session for user2, got %d", len(user2Sessions))
	}
}

func TestMemoryResumeStorage_ExpiredSession(t *testing.T) {
	storage := NewMemoryResumeStorage()
	
	session := &UploadSession{
		ID:       "expired-session",
		UserID:   "user1",
		FileName: "test.txt",
		FileSize: 1024,
		ExpiresAt: time.Now().Add(-time.Hour), // 已过期
	}
	
	err := storage.CreateSession(session)
	if err != nil {
		t.Fatalf("Failed to create session: %v", err)
	}
	
	// 尝试获取过期会话
	_, err = storage.GetSession("expired-session")
	if err == nil {
		t.Error("Expected error for expired session, got nil")
	}
}

func TestMemoryResumeStorage_CleanupExpiredSessions(t *testing.T) {
	storage := NewMemoryResumeStorage()
	
	// 创建过期和未过期的会话
	expiredSession := &UploadSession{
		ID:       "expired-session",
		UserID:   "user1",
		FileName: "expired.txt",
		FileSize: 1024,
		ExpiresAt: time.Now().Add(-time.Hour),
	}
	
	activeSession := &UploadSession{
		ID:       "active-session",
		UserID:   "user1",
		FileName: "active.txt",
		FileSize: 1024,
		ExpiresAt: time.Now().Add(time.Hour),
	}
	
	storage.CreateSession(expiredSession)
	storage.CreateSession(activeSession)
	
	// 执行清理
	err := storage.CleanupExpiredSessions()
	if err != nil {
		t.Fatalf("Failed to cleanup expired sessions: %v", err)
	}
	
	// 验证过期会话被删除
	storage.mu.RLock()
	_, expiredExists := storage.sessions["expired-session"]
	_, activeExists := storage.sessions["active-session"]
	storage.mu.RUnlock()
	
	if expiredExists {
		t.Error("Expected expired session to be deleted")
	}
	
	if !activeExists {
		t.Error("Expected active session to remain")
	}
}

func TestMemoryResumeStorage_DeleteSession(t *testing.T) {
	storage := NewMemoryResumeStorage()
	
	session := &UploadSession{
		ID:       "test-session",
		UserID:   "user1",
		FileName: "test.txt",
		FileSize: 1024,
		ExpiresAt: time.Now().Add(time.Hour),
	}
	
	err := storage.CreateSession(session)
	if err != nil {
		t.Fatalf("Failed to create session: %v", err)
	}
	
	// 删除会话
	err = storage.DeleteSession("test-session")
	if err != nil {
		t.Fatalf("Failed to delete session: %v", err)
	}
	
	// 验证会话已删除
	_, err = storage.GetSession("test-session")
	if err == nil {
		t.Error("Expected error for deleted session, got nil")
	}
}

func TestMemoryResumeStorage_GetSessionStats(t *testing.T) {
	storage := NewMemoryResumeStorage()
	
	// 创建不同状态的会话
	sessions := []*UploadSession{
		{
			ID:       "active-1",
			UserID:   "user1",
			FileName: "file1.txt",
			FileSize: 1024,
			Status:   StatusActive,
			ExpiresAt: time.Now().Add(time.Hour),
		},
		{
			ID:       "active-2",
			UserID:   "user1",
			FileName: "file2.txt",
			FileSize: 2048,
			Status:   StatusActive,
			ExpiresAt: time.Now().Add(time.Hour),
		},
		{
			ID:       "completed-1",
			UserID:   "user2",
			FileName: "file3.txt",
			FileSize: 512,
			Status:   StatusCompleted,
			UploadedBytes: 512,
			ExpiresAt: time.Now().Add(time.Hour),
		},
	}
	
	for _, session := range sessions {
		err := storage.CreateSession(session)
		if err != nil {
			t.Fatalf("Failed to create session %s: %v", session.ID, err)
		}
	}
	
	stats := storage.GetSessionStats()
	
	if stats["total_sessions"] != 3 {
		t.Errorf("Expected 3 total sessions, got %v", stats["total_sessions"])
	}
	
	statusDist := stats["status_distribution"].(map[SessionStatus]int)
	if statusDist[StatusActive] != 2 {
		t.Errorf("Expected 2 active sessions, got %d", statusDist[StatusActive])
	}
	
	if statusDist[StatusCompleted] != 1 {
		t.Errorf("Expected 1 completed session, got %d", statusDist[StatusCompleted])
	}
	
	expectedTotalBytes := int64(1024 + 2048 + 512)
	if stats["total_bytes"] != expectedTotalBytes {
		t.Errorf("Expected total bytes %d, got %v", expectedTotalBytes, stats["total_bytes"])
	}
}
