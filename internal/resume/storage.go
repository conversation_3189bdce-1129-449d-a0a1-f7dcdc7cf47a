package resume

import (
	"fmt"
	"sync"
	"time"

	log "github.com/sirupsen/logrus"
)

// UploadSession 上传会话
type UploadSession struct {
	ID              string            `json:"id"`
	UserID          string            `json:"user_id"`
	FileName        string            `json:"file_name"`
	FilePath        string            `json:"file_path"`
	FileSize        int64             `json:"file_size"`
	ChunkSize       int64             `json:"chunk_size"`
	TotalChunks     int64             `json:"total_chunks"`
	CompletedChunks map[int64]bool    `json:"completed_chunks"`
	UploadedBytes   int64             `json:"uploaded_bytes"`
	CreatedAt       time.Time         `json:"created_at"`
	UpdatedAt       time.Time         `json:"updated_at"`
	ExpiresAt       time.Time         `json:"expires_at"`
	Status          SessionStatus     `json:"status"`
	Metadata        map[string]string `json:"metadata"`
	Checksum        string            `json:"checksum"`
	ChunkChecksums  map[int64]string  `json:"chunk_checksums"`
}

// SessionStatus 会话状态
type SessionStatus string

const (
	StatusActive    SessionStatus = "active"
	StatusCompleted SessionStatus = "completed"
	StatusFailed    SessionStatus = "failed"
	StatusExpired   SessionStatus = "expired"
	StatusCancelled SessionStatus = "cancelled"
)

// ResumeStorage 断点续传存储接口
type ResumeStorage interface {
	// 会话管理
	CreateSession(session *UploadSession) error
	GetSession(sessionID string) (*UploadSession, error)
	UpdateSession(session *UploadSession) error
	DeleteSession(sessionID string) error
	ListSessions(userID string) ([]*UploadSession, error)
	
	// 分片管理
	MarkChunkCompleted(sessionID string, chunkIndex int64, checksum string) error
	GetCompletedChunks(sessionID string) (map[int64]bool, error)
	
	// 清理
	CleanupExpiredSessions() error
}

// MemoryResumeStorage 内存存储实现（用于开发和测试）
type MemoryResumeStorage struct {
	sessions map[string]*UploadSession
	mu       sync.RWMutex
}

// NewMemoryResumeStorage 创建内存存储
func NewMemoryResumeStorage() *MemoryResumeStorage {
	storage := &MemoryResumeStorage{
		sessions: make(map[string]*UploadSession),
	}
	
	// 启动清理协程
	go storage.startCleanupRoutine()
	
	return storage
}

// CreateSession 创建上传会话
func (mrs *MemoryResumeStorage) CreateSession(session *UploadSession) error {
	mrs.mu.Lock()
	defer mrs.mu.Unlock()
	
	if _, exists := mrs.sessions[session.ID]; exists {
		return fmt.Errorf("session %s already exists", session.ID)
	}
	
	// 设置默认值
	now := time.Now()
	session.CreatedAt = now
	session.UpdatedAt = now
	if session.Status == "" {
		session.Status = StatusActive
	}
	
	if session.CompletedChunks == nil {
		session.CompletedChunks = make(map[int64]bool)
	}
	
	if session.ChunkChecksums == nil {
		session.ChunkChecksums = make(map[int64]string)
	}
	
	if session.Metadata == nil {
		session.Metadata = make(map[string]string)
	}
	
	// 创建副本以避免外部修改
	sessionCopy := *session
	mrs.sessions[session.ID] = &sessionCopy
	
	log.Infof("Created upload session %s for user %s, file %s (%d bytes)", 
		session.ID, session.UserID, session.FileName, session.FileSize)
	
	return nil
}

// GetSession 获取上传会话
func (mrs *MemoryResumeStorage) GetSession(sessionID string) (*UploadSession, error) {
	mrs.mu.RLock()
	defer mrs.mu.RUnlock()
	
	session, exists := mrs.sessions[sessionID]
	if !exists {
		return nil, fmt.Errorf("session %s not found", sessionID)
	}
	
	// 检查是否过期
	if time.Now().After(session.ExpiresAt) {
		return nil, fmt.Errorf("session %s has expired", sessionID)
	}
	
	// 返回副本
	sessionCopy := *session
	return &sessionCopy, nil
}

// UpdateSession 更新上传会话
func (mrs *MemoryResumeStorage) UpdateSession(session *UploadSession) error {
	mrs.mu.Lock()
	defer mrs.mu.Unlock()

	_, exists := mrs.sessions[session.ID]
	if !exists {
		return fmt.Errorf("session %s not found", session.ID)
	}

	// 更新时间戳
	session.UpdatedAt = time.Now()

	// 创建副本并更新
	sessionCopy := *session
	mrs.sessions[session.ID] = &sessionCopy

	log.Debugf("Updated upload session %s, uploaded bytes: %d/%d",
		session.ID, session.UploadedBytes, session.FileSize)

	// 检查是否完成
	if session.UploadedBytes >= session.FileSize {
		session.Status = StatusCompleted
		log.Infof("Upload session %s completed", session.ID)
	}

	return nil
}

// DeleteSession 删除上传会话
func (mrs *MemoryResumeStorage) DeleteSession(sessionID string) error {
	mrs.mu.Lock()
	defer mrs.mu.Unlock()
	
	if _, exists := mrs.sessions[sessionID]; !exists {
		return fmt.Errorf("session %s not found", sessionID)
	}
	
	delete(mrs.sessions, sessionID)
	log.Infof("Deleted upload session %s", sessionID)
	
	return nil
}

// ListSessions 列出用户的上传会话
func (mrs *MemoryResumeStorage) ListSessions(userID string) ([]*UploadSession, error) {
	mrs.mu.RLock()
	defer mrs.mu.RUnlock()
	
	var sessions []*UploadSession
	for _, session := range mrs.sessions {
		if session.UserID == userID {
			sessionCopy := *session
			sessions = append(sessions, &sessionCopy)
		}
	}
	
	return sessions, nil
}

// MarkChunkCompleted 标记分片完成
func (mrs *MemoryResumeStorage) MarkChunkCompleted(sessionID string, chunkIndex int64, checksum string) error {
	mrs.mu.Lock()
	defer mrs.mu.Unlock()
	
	session, exists := mrs.sessions[sessionID]
	if !exists {
		return fmt.Errorf("session %s not found", sessionID)
	}
	
	// 标记分片完成
	session.CompletedChunks[chunkIndex] = true
	session.ChunkChecksums[chunkIndex] = checksum
	session.UpdatedAt = time.Now()
	
	// 更新已上传字节数
	session.UploadedBytes = int64(len(session.CompletedChunks)) * session.ChunkSize
	if session.UploadedBytes > session.FileSize {
		session.UploadedBytes = session.FileSize
	}
	
	log.Debugf("Marked chunk %d completed for session %s, progress: %d/%d", 
		chunkIndex, sessionID, len(session.CompletedChunks), session.TotalChunks)
	
	return nil
}

// GetCompletedChunks 获取已完成的分片
func (mrs *MemoryResumeStorage) GetCompletedChunks(sessionID string) (map[int64]bool, error) {
	mrs.mu.RLock()
	defer mrs.mu.RUnlock()
	
	session, exists := mrs.sessions[sessionID]
	if !exists {
		return nil, fmt.Errorf("session %s not found", sessionID)
	}
	
	// 返回副本
	chunks := make(map[int64]bool)
	for k, v := range session.CompletedChunks {
		chunks[k] = v
	}
	
	return chunks, nil
}

// CleanupExpiredSessions 清理过期会话
func (mrs *MemoryResumeStorage) CleanupExpiredSessions() error {
	mrs.mu.Lock()
	defer mrs.mu.Unlock()
	
	now := time.Now()
	var expiredSessions []string
	
	for sessionID, session := range mrs.sessions {
		if now.After(session.ExpiresAt) {
			expiredSessions = append(expiredSessions, sessionID)
		}
	}
	
	for _, sessionID := range expiredSessions {
		delete(mrs.sessions, sessionID)
		log.Infof("Cleaned up expired session %s", sessionID)
	}
	
	if len(expiredSessions) > 0 {
		log.Infof("Cleaned up %d expired sessions", len(expiredSessions))
	}
	
	return nil
}

// startCleanupRoutine 启动清理协程
func (mrs *MemoryResumeStorage) startCleanupRoutine() {
	ticker := time.NewTicker(10 * time.Minute)
	defer ticker.Stop()
	
	for range ticker.C {
		if err := mrs.CleanupExpiredSessions(); err != nil {
			log.Errorf("Failed to cleanup expired sessions: %v", err)
		}
	}
}

// GetSessionStats 获取会话统计信息
func (mrs *MemoryResumeStorage) GetSessionStats() map[string]interface{} {
	mrs.mu.RLock()
	defer mrs.mu.RUnlock()
	
	stats := map[string]interface{}{
		"total_sessions": len(mrs.sessions),
		"status_distribution": make(map[SessionStatus]int),
		"total_bytes": int64(0),
		"completed_bytes": int64(0),
	}
	
	statusDist := stats["status_distribution"].(map[SessionStatus]int)
	
	for _, session := range mrs.sessions {
		statusDist[session.Status]++
		stats["total_bytes"] = stats["total_bytes"].(int64) + session.FileSize
		stats["completed_bytes"] = stats["completed_bytes"].(int64) + session.UploadedBytes
	}
	
	return stats
}

// 全局存储实例
var GlobalResumeStorage ResumeStorage

// InitResumeStorage 初始化断点续传存储
func InitResumeStorage() {
	GlobalResumeStorage = NewMemoryResumeStorage()
	log.Info("Resume storage initialized")
}
