package streaming

import (
	"context"
	"crypto/md5"
	"fmt"
	"io"
	"sync"
	"sync/atomic"
	"time"

	"github.com/alist-org/alist/v3/internal/memory"
	"github.com/alist-org/alist/v3/internal/resume"
	log "github.com/sirupsen/logrus"
)

// ChunkProcessor 流式分片处理器
type ChunkProcessor struct {
	sessionID     string
	chunkSize     int64
	maxConcurrent int
	bufferPool    *memory.BufferPool
	progressMgr   *resume.UploadProgressManager
	metrics       *ProcessorMetrics
}

// ProcessorMetrics 处理器指标
type ProcessorMetrics struct {
	TotalChunks       int64         `json:"total_chunks"`
	ProcessedChunks   int64         `json:"processed_chunks"`
	FailedChunks      int64         `json:"failed_chunks"`
	TotalBytes        int64         `json:"total_bytes"`
	ProcessedBytes    int64         `json:"processed_bytes"`
	AverageChunkTime  time.Duration `json:"average_chunk_time"`
	PeakConcurrency   int64         `json:"peak_concurrency"`
	CurrentConcurrency int64        `json:"current_concurrency"`
	StartTime         time.Time     `json:"start_time"`
	EndTime           time.Time     `json:"end_time"`
	mu                sync.RWMutex
}

// ChunkTask 分片任务
type ChunkTask struct {
	Index     int64
	Data      []byte
	Checksum  string
	Offset    int64
	Size      int64
	Retries   int
	CreatedAt time.Time
}

// ChunkResult 分片处理结果
type ChunkResult struct {
	Task      *ChunkTask
	Success   bool
	Error     error
	Duration  time.Duration
	Timestamp time.Time
}

// NewChunkProcessor 创建流式分片处理器
func NewChunkProcessor(sessionID string, chunkSize int64, maxConcurrent int) *ChunkProcessor {
	return &ChunkProcessor{
		sessionID:     sessionID,
		chunkSize:     chunkSize,
		maxConcurrent: maxConcurrent,
		bufferPool:    memory.GlobalBufferPool,
		progressMgr:   resume.GlobalProgressManager,
		metrics: &ProcessorMetrics{
			StartTime: time.Now(),
		},
	}
}

// ProcessStream 处理流式数据
func (cp *ChunkProcessor) ProcessStream(ctx context.Context, reader io.Reader, 
	processor func(*ChunkTask) error) error {
	
	// 创建任务通道
	taskChan := make(chan *ChunkTask, cp.maxConcurrent*2)
	resultChan := make(chan *ChunkResult, cp.maxConcurrent*2)
	
	// 启动工作协程池
	var wg sync.WaitGroup
	for i := 0; i < cp.maxConcurrent; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			cp.worker(ctx, workerID, taskChan, resultChan, processor)
		}(i)
	}
	
	// 启动结果收集协程
	var resultWg sync.WaitGroup
	resultWg.Add(1)
	go func() {
		defer resultWg.Done()
		cp.resultCollector(ctx, resultChan)
	}()
	
	// 读取并分片数据
	err := cp.readAndChunk(ctx, reader, taskChan)
	
	// 关闭任务通道，等待所有工作协程完成
	close(taskChan)
	wg.Wait()
	
	// 关闭结果通道，等待结果收集完成
	close(resultChan)
	resultWg.Wait()
	
	cp.metrics.mu.Lock()
	cp.metrics.EndTime = time.Now()
	cp.metrics.mu.Unlock()
	
	return err
}

// readAndChunk 读取数据并分片
func (cp *ChunkProcessor) readAndChunk(ctx context.Context, reader io.Reader,
	taskChan chan<- *ChunkTask) error {

	log.Debugf("readAndChunk started with chunk size: %d", cp.chunkSize)

	var chunkIndex int64
	var totalOffset int64
	
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		log.Debugf("Reading chunk %d (offset: %d)", chunkIndex, totalOffset)

		// 从缓冲区池获取缓冲区
		var buffer []byte
		var err error
		
		if cp.bufferPool != nil {
			buffer, err = cp.bufferPool.Get(int(cp.chunkSize))
			if err != nil {
				log.Warnf("Failed to get buffer from pool: %v, using regular allocation", err)
				buffer = make([]byte, cp.chunkSize)
			}
		} else {
			buffer = make([]byte, cp.chunkSize)
		}
		
		// 读取数据
		n, err := io.ReadFull(reader, buffer)
		if err != nil {
			if err == io.EOF || err == io.ErrUnexpectedEOF {
				// 处理最后一个分片
				if n > 0 {
					finalChunk := cp.createChunkTask(chunkIndex, buffer[:n], totalOffset)
					select {
					case taskChan <- finalChunk:
						atomic.AddInt64(&cp.metrics.TotalChunks, 1)
						atomic.AddInt64(&cp.metrics.TotalBytes, int64(n))
						log.Debugf("Sent final chunk %d with %d bytes", chunkIndex, n)
					case <-ctx.Done():
						if cp.bufferPool != nil {
							cp.bufferPool.Put(buffer)
						}
						return ctx.Err()
					}
				} else {
					// 归还空缓冲区
					if cp.bufferPool != nil {
						cp.bufferPool.Put(buffer)
					}
				}
				log.Debugf("Finished reading data, total chunks: %d", chunkIndex+1)
				break
			}

			// 归还缓冲区
			if cp.bufferPool != nil {
				cp.bufferPool.Put(buffer)
			}
			return fmt.Errorf("failed to read chunk %d: %w", chunkIndex, err)
		}
		
		// 创建分片任务
		task := cp.createChunkTask(chunkIndex, buffer[:n], totalOffset)

		select {
		case taskChan <- task:
			atomic.AddInt64(&cp.metrics.TotalChunks, 1)
			atomic.AddInt64(&cp.metrics.TotalBytes, int64(n))
			log.Debugf("Sent chunk %d with %d bytes (offset: %d)", chunkIndex, n, totalOffset)
			chunkIndex++
			totalOffset += int64(n)
		case <-ctx.Done():
			if cp.bufferPool != nil {
				cp.bufferPool.Put(buffer)
			}
			return ctx.Err()
		}
	}
	
	return nil
}

// createChunkTask 创建分片任务
func (cp *ChunkProcessor) createChunkTask(index int64, data []byte, offset int64) *ChunkTask {
	// 计算校验和
	checksum := fmt.Sprintf("%x", md5.Sum(data))
	
	return &ChunkTask{
		Index:     index,
		Data:      data,
		Checksum:  checksum,
		Offset:    offset,
		Size:      int64(len(data)),
		Retries:   0,
		CreatedAt: time.Now(),
	}
}

// worker 工作协程
func (cp *ChunkProcessor) worker(ctx context.Context, workerID int, 
	taskChan <-chan *ChunkTask, resultChan chan<- *ChunkResult, 
	processor func(*ChunkTask) error) {
	
	log.Debugf("Chunk processor worker %d started", workerID)
	defer log.Debugf("Chunk processor worker %d stopped", workerID)
	
	for task := range taskChan {
		select {
		case <-ctx.Done():
			return
		default:
		}
		
		// 更新并发度指标
		current := atomic.AddInt64(&cp.metrics.CurrentConcurrency, 1)
		for {
			peak := atomic.LoadInt64(&cp.metrics.PeakConcurrency)
			if current <= peak || atomic.CompareAndSwapInt64(&cp.metrics.PeakConcurrency, peak, current) {
				break
			}
		}
		
		startTime := time.Now()
		
		// 处理分片
		err := processor(task)
		
		duration := time.Since(startTime)
		
		// 创建结果
		result := &ChunkResult{
			Task:      task,
			Success:   err == nil,
			Error:     err,
			Duration:  duration,
			Timestamp: time.Now(),
		}
		
		// 发送结果
		select {
		case resultChan <- result:
		case <-ctx.Done():
			atomic.AddInt64(&cp.metrics.CurrentConcurrency, -1)
			return
		}
		
		// 更新并发度
		atomic.AddInt64(&cp.metrics.CurrentConcurrency, -1)
		
		// 归还缓冲区
		if cp.bufferPool != nil {
			cp.bufferPool.Put(task.Data)
		}
	}
}

// resultCollector 结果收集器
func (cp *ChunkProcessor) resultCollector(ctx context.Context, resultChan <-chan *ChunkResult) {
	var totalDuration time.Duration
	var processedCount int64
	
	for result := range resultChan {
		select {
		case <-ctx.Done():
			return
		default:
		}
		
		if result.Success {
			// 标记分片完成
			if cp.progressMgr != nil {
				err := cp.progressMgr.MarkChunkCompleted(cp.sessionID, result.Task.Index, result.Task.Data)
				if err != nil {
					log.Errorf("Failed to mark chunk %d completed: %v", result.Task.Index, err)
				} else {
					log.Debugf("Successfully marked chunk %d completed for session %s", result.Task.Index, cp.sessionID)
				}
			}
			
			atomic.AddInt64(&cp.metrics.ProcessedChunks, 1)
			atomic.AddInt64(&cp.metrics.ProcessedBytes, result.Task.Size)
			
			// 更新平均处理时间
			processedCount++
			totalDuration += result.Duration
			
			cp.metrics.mu.Lock()
			cp.metrics.AverageChunkTime = totalDuration / time.Duration(processedCount)
			cp.metrics.mu.Unlock()
			
			log.Debugf("Chunk %d processed successfully in %v", result.Task.Index, result.Duration)
		} else {
			atomic.AddInt64(&cp.metrics.FailedChunks, 1)
			log.Errorf("Chunk %d processing failed: %v", result.Task.Index, result.Error)
		}
	}
}

// GetMetrics 获取处理器指标
func (cp *ChunkProcessor) GetMetrics() *ProcessorMetrics {
	cp.metrics.mu.RLock()
	defer cp.metrics.mu.RUnlock()
	
	return &ProcessorMetrics{
		TotalChunks:        atomic.LoadInt64(&cp.metrics.TotalChunks),
		ProcessedChunks:    atomic.LoadInt64(&cp.metrics.ProcessedChunks),
		FailedChunks:       atomic.LoadInt64(&cp.metrics.FailedChunks),
		TotalBytes:         atomic.LoadInt64(&cp.metrics.TotalBytes),
		ProcessedBytes:     atomic.LoadInt64(&cp.metrics.ProcessedBytes),
		AverageChunkTime:   cp.metrics.AverageChunkTime,
		PeakConcurrency:    atomic.LoadInt64(&cp.metrics.PeakConcurrency),
		CurrentConcurrency: atomic.LoadInt64(&cp.metrics.CurrentConcurrency),
		StartTime:          cp.metrics.StartTime,
		EndTime:            cp.metrics.EndTime,
	}
}

// GetProgress 获取处理进度
func (cp *ChunkProcessor) GetProgress() float64 {
	total := atomic.LoadInt64(&cp.metrics.TotalChunks)
	processed := atomic.LoadInt64(&cp.metrics.ProcessedChunks)
	
	if total == 0 {
		return 0
	}
	
	return float64(processed) / float64(total) * 100
}

// GetThroughput 获取处理吞吐量（字节/秒）
func (cp *ChunkProcessor) GetThroughput() float64 {
	cp.metrics.mu.RLock()
	defer cp.metrics.mu.RUnlock()
	
	if cp.metrics.StartTime.IsZero() {
		return 0
	}
	
	endTime := cp.metrics.EndTime
	if endTime.IsZero() {
		endTime = time.Now()
	}
	
	duration := endTime.Sub(cp.metrics.StartTime)
	if duration == 0 {
		return 0
	}
	
	processedBytes := atomic.LoadInt64(&cp.metrics.ProcessedBytes)
	return float64(processedBytes) / duration.Seconds()
}
