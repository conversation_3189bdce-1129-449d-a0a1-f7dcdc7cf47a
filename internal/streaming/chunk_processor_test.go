package streaming

import (
	"bytes"
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/alist-org/alist/v3/internal/memory"
	"github.com/alist-org/alist/v3/internal/resume"
)

func TestChunkProcessor_ProcessStream(t *testing.T) {
	// 初始化依赖
	memory.InitBufferPool(10) // 10MB
	resume.InitResumeStorage()
	resume.InitProgressManager()
	
	// 创建测试会话
	session, err := resume.GlobalProgressManager.CreateUploadSession(
		"test_user", "test_file.txt", "/test/test_file.txt", 1024, 256, nil)
	if err != nil {
		t.Fatalf("Failed to create upload session: %v", err)
	}
	
	// 创建分片处理器
	processor := NewChunkProcessor(session.ID, 256, 2)
	
	// 创建测试数据
	testData := make([]byte, 1024)
	for i := range testData {
		testData[i] = byte(i % 256)
	}
	reader := bytes.NewReader(testData)
	
	// 处理计数器
	var processedChunks int
	var mu sync.Mutex
	
	// 定义处理函数
	chunkProcessor := func(task *ChunkTask) error {
		mu.Lock()
		processedChunks++
		mu.Unlock()
		
		// 模拟处理时间
		time.Sleep(10 * time.Millisecond)
		
		t.Logf("Processing chunk %d, size: %d, checksum: %s", 
			task.Index, task.Size, task.Checksum)
		
		return nil
	}
	
	// 执行流式处理
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	
	err = processor.ProcessStream(ctx, reader, chunkProcessor)
	if err != nil {
		t.Fatalf("ProcessStream failed: %v", err)
	}
	
	// 验证结果
	metrics := processor.GetMetrics()
	
	if metrics.TotalChunks != 4 {
		t.Errorf("Expected 4 total chunks, got %d", metrics.TotalChunks)
	}
	
	if metrics.ProcessedChunks != 4 {
		t.Errorf("Expected 4 processed chunks, got %d", metrics.ProcessedChunks)
	}
	
	if metrics.TotalBytes != 1024 {
		t.Errorf("Expected 1024 total bytes, got %d", metrics.TotalBytes)
	}
	
	if metrics.ProcessedBytes != 1024 {
		t.Errorf("Expected 1024 processed bytes, got %d", metrics.ProcessedBytes)
	}
	
	mu.Lock()
	actualProcessed := processedChunks
	mu.Unlock()
	
	if actualProcessed != 4 {
		t.Errorf("Expected 4 chunks to be processed by handler, got %d", actualProcessed)
	}
	
	progress := processor.GetProgress()
	if progress != 100.0 {
		t.Errorf("Expected 100%% progress, got %.2f%%", progress)
	}
}

func TestChunkProcessor_ProcessStreamWithErrors(t *testing.T) {
	// 初始化依赖
	memory.InitBufferPool(10)
	resume.InitResumeStorage()
	resume.InitProgressManager()
	
	// 创建测试会话
	session, err := resume.GlobalProgressManager.CreateUploadSession(
		"test_user", "test_file.txt", "/test/test_file.txt", 512, 128, nil)
	if err != nil {
		t.Fatalf("Failed to create upload session: %v", err)
	}
	
	// 创建分片处理器
	processor := NewChunkProcessor(session.ID, 128, 2)
	
	// 创建测试数据
	testData := make([]byte, 512)
	reader := bytes.NewReader(testData)
	
	// 定义会失败的处理函数
	chunkProcessor := func(task *ChunkTask) error {
		if task.Index == 1 {
			return fmt.Errorf("simulated error for chunk %d", task.Index)
		}
		return nil
	}
	
	// 执行流式处理
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	
	err = processor.ProcessStream(ctx, reader, chunkProcessor)
	if err != nil {
		t.Fatalf("ProcessStream failed: %v", err)
	}
	
	// 验证结果
	metrics := processor.GetMetrics()
	
	if metrics.TotalChunks != 4 {
		t.Errorf("Expected 4 total chunks, got %d", metrics.TotalChunks)
	}
	
	if metrics.ProcessedChunks != 3 {
		t.Errorf("Expected 3 processed chunks, got %d", metrics.ProcessedChunks)
	}
	
	if metrics.FailedChunks != 1 {
		t.Errorf("Expected 1 failed chunk, got %d", metrics.FailedChunks)
	}
}

func TestChunkProcessor_ProcessStreamCancellation(t *testing.T) {
	// 初始化依赖
	memory.InitBufferPool(10)
	resume.InitResumeStorage()
	resume.InitProgressManager()

	// 创建测试会话（更大的文件）
	session, err := resume.GlobalProgressManager.CreateUploadSession(
		"test_user", "test_file.txt", "/test/test_file.txt", 4096, 256, nil)
	if err != nil {
		t.Fatalf("Failed to create upload session: %v", err)
	}

	// 创建分片处理器
	processor := NewChunkProcessor(session.ID, 256, 1) // 降低并发度

	// 创建测试数据（更大）
	testData := make([]byte, 4096)
	reader := bytes.NewReader(testData)

	// 定义慢处理函数
	chunkProcessor := func(task *ChunkTask) error {
		time.Sleep(500 * time.Millisecond) // 更长的处理时间
		return nil
	}

	// 创建会被取消的上下文（更短的超时）
	ctx, cancel := context.WithTimeout(context.Background(), 50*time.Millisecond)
	defer cancel()

	// 执行流式处理（应该被取消）
	err = processor.ProcessStream(ctx, reader, chunkProcessor)

	// 验证取消（可能是DeadlineExceeded或Canceled）
	if err != context.DeadlineExceeded && err != context.Canceled {
		t.Errorf("Expected context.DeadlineExceeded or context.Canceled, got %v", err)
	}
}

func TestChunkProcessor_Metrics(t *testing.T) {
	// 创建分片处理器
	processor := NewChunkProcessor("test-session", 256, 2)
	
	// 获取初始指标
	metrics := processor.GetMetrics()
	
	if metrics.TotalChunks != 0 {
		t.Errorf("Expected 0 total chunks initially, got %d", metrics.TotalChunks)
	}
	
	if metrics.ProcessedChunks != 0 {
		t.Errorf("Expected 0 processed chunks initially, got %d", metrics.ProcessedChunks)
	}
	
	if metrics.StartTime.IsZero() {
		t.Error("Expected start time to be set")
	}
	
	// 测试进度计算
	progress := processor.GetProgress()
	if progress != 0 {
		t.Errorf("Expected 0%% progress initially, got %.2f%%", progress)
	}
	
	// 测试吞吐量计算
	throughput := processor.GetThroughput()
	if throughput != 0 {
		t.Errorf("Expected 0 throughput initially, got %.2f", throughput)
	}
}

func TestChunkProcessor_ConcurrentProcessing(t *testing.T) {
	// 初始化依赖
	memory.InitBufferPool(10)
	resume.InitResumeStorage()
	resume.InitProgressManager()
	
	// 创建测试会话
	session, err := resume.GlobalProgressManager.CreateUploadSession(
		"test_user", "test_file.txt", "/test/test_file.txt", 2048, 256, nil)
	if err != nil {
		t.Fatalf("Failed to create upload session: %v", err)
	}
	
	// 创建分片处理器（高并发）
	processor := NewChunkProcessor(session.ID, 256, 4)
	
	// 创建测试数据
	testData := make([]byte, 2048)
	reader := bytes.NewReader(testData)
	
	// 记录并发度
	var maxConcurrent int
	var currentConcurrent int
	var mu sync.Mutex
	
	// 定义处理函数
	chunkProcessor := func(task *ChunkTask) error {
		mu.Lock()
		currentConcurrent++
		if currentConcurrent > maxConcurrent {
			maxConcurrent = currentConcurrent
		}
		mu.Unlock()
		
		// 模拟处理时间
		time.Sleep(50 * time.Millisecond)
		
		mu.Lock()
		currentConcurrent--
		mu.Unlock()
		
		return nil
	}
	
	// 执行流式处理
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	
	err = processor.ProcessStream(ctx, reader, chunkProcessor)
	if err != nil {
		t.Fatalf("ProcessStream failed: %v", err)
	}
	
	// 验证并发度
	mu.Lock()
	actualMaxConcurrent := maxConcurrent
	mu.Unlock()
	
	if actualMaxConcurrent < 2 {
		t.Errorf("Expected at least 2 concurrent processing, got %d", actualMaxConcurrent)
	}
	
	// 验证指标中的峰值并发度
	metrics := processor.GetMetrics()
	if metrics.PeakConcurrency < 2 {
		t.Errorf("Expected peak concurrency >= 2, got %d", metrics.PeakConcurrency)
	}
}

func TestChunkProcessor_LargeFile(t *testing.T) {
	// 初始化依赖
	memory.InitBufferPool(50) // 50MB
	resume.InitResumeStorage()
	resume.InitProgressManager()
	
	// 创建测试会话（大文件）
	fileSize := int64(10 * 1024 * 1024) // 10MB
	chunkSize := int64(1024 * 1024)     // 1MB
	
	session, err := resume.GlobalProgressManager.CreateUploadSession(
		"test_user", "large_file.txt", "/test/large_file.txt", fileSize, chunkSize, nil)
	if err != nil {
		t.Fatalf("Failed to create upload session: %v", err)
	}
	
	// 创建分片处理器
	processor := NewChunkProcessor(session.ID, chunkSize, 3)
	
	// 创建大文件数据
	testData := make([]byte, fileSize)
	for i := range testData {
		testData[i] = byte(i % 256)
	}
	reader := bytes.NewReader(testData)
	
	// 定义处理函数
	chunkProcessor := func(task *ChunkTask) error {
		// 验证分片大小
		expectedSize := chunkSize
		if task.Index == 9 { // 最后一个分片
			expectedSize = fileSize % chunkSize
			if expectedSize == 0 {
				expectedSize = chunkSize
			}
		}
		
		if task.Size != expectedSize {
			return fmt.Errorf("unexpected chunk size: expected %d, got %d", expectedSize, task.Size)
		}
		
		return nil
	}
	
	// 执行流式处理
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	startTime := time.Now()
	err = processor.ProcessStream(ctx, reader, chunkProcessor)
	duration := time.Since(startTime)
	
	if err != nil {
		t.Fatalf("ProcessStream failed: %v", err)
	}
	
	// 验证结果
	metrics := processor.GetMetrics()
	expectedChunks := fileSize / chunkSize
	
	if metrics.TotalChunks != expectedChunks {
		t.Errorf("Expected %d total chunks, got %d", expectedChunks, metrics.TotalChunks)
	}
	
	if metrics.ProcessedChunks != expectedChunks {
		t.Errorf("Expected %d processed chunks, got %d", expectedChunks, metrics.ProcessedChunks)
	}
	
	if metrics.TotalBytes != fileSize {
		t.Errorf("Expected %d total bytes, got %d", fileSize, metrics.TotalBytes)
	}
	
	// 计算吞吐量
	throughput := processor.GetThroughput()
	expectedThroughput := float64(fileSize) / duration.Seconds()
	
	t.Logf("Processing completed in %v, throughput: %.2f MB/s (expected: %.2f MB/s)", 
		duration, throughput/(1024*1024), expectedThroughput/(1024*1024))
	
	if throughput <= 0 {
		t.Error("Expected positive throughput")
	}
}
