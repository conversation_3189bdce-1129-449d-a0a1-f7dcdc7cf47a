package streaming

import (
	"context"
	"fmt"
	"io"
	"sync"
	"time"

	"github.com/alist-org/alist/v3/internal/resume"
	"github.com/alist-org/alist/v3/internal/retry"
	log "github.com/sirupsen/logrus"
)

// StreamingUploadManager 流式上传管理器
type StreamingUploadManager struct {
	progressMgr   *resume.UploadProgressManager
	retryPolicy   *retry.RetryPolicy
	activeUploads map[string]*UploadContext
	mu            sync.RWMutex
}

// UploadContext 上传上下文
type UploadContext struct {
	SessionID     string
	Processor     *ChunkProcessor
	Context       context.Context
	Cancel        context.CancelFunc
	StartTime     time.Time
	Status        UploadStatus
	Error         error
	mu            sync.RWMutex
}

// UploadStatus 上传状态
type UploadStatus string

const (
	StatusPending    UploadStatus = "pending"
	StatusProcessing UploadStatus = "processing"
	StatusCompleted  UploadStatus = "completed"
	StatusFailed     UploadStatus = "failed"
	StatusCancelled  UploadStatus = "cancelled"
)

// UploadConfig 上传配置
type UploadConfig struct {
	ChunkSize     int64 `json:"chunk_size"`
	MaxConcurrent int   `json:"max_concurrent"`
	EnableRetry   bool  `json:"enable_retry"`
	MaxRetries    int   `json:"max_retries"`
	Timeout       time.Duration `json:"timeout"`
}

// DefaultUploadConfig 默认上传配置
func DefaultUploadConfig() *UploadConfig {
	return &UploadConfig{
		ChunkSize:     4 * 1024 * 1024, // 4MB
		MaxConcurrent: 3,
		EnableRetry:   true,
		MaxRetries:    3,
		Timeout:       30 * time.Minute,
	}
}

// NewStreamingUploadManager 创建流式上传管理器
func NewStreamingUploadManager() *StreamingUploadManager {
	return &StreamingUploadManager{
		progressMgr:   resume.GlobalProgressManager,
		retryPolicy:   retry.UploadRetryPolicy(),
		activeUploads: make(map[string]*UploadContext),
	}
}

// StartStreamingUpload 开始流式上传
func (sum *StreamingUploadManager) StartStreamingUpload(sessionID string, reader io.Reader, 
	config *UploadConfig) error {
	
	if config == nil {
		config = DefaultUploadConfig()
	}
	
	// 检查会话是否存在
	session, err := sum.progressMgr.ResumeUploadSession(sessionID)
	if err != nil {
		return fmt.Errorf("failed to resume session: %w", err)
	}
	
	// 检查是否已经在上传
	sum.mu.Lock()
	if _, exists := sum.activeUploads[sessionID]; exists {
		sum.mu.Unlock()
		return fmt.Errorf("upload already in progress for session %s", sessionID)
	}
	
	// 创建上传上下文
	ctx, cancel := context.WithTimeout(context.Background(), config.Timeout)
	uploadCtx := &UploadContext{
		SessionID: sessionID,
		Context:   ctx,
		Cancel:    cancel,
		StartTime: time.Now(),
		Status:    StatusPending,
	}
	
	sum.activeUploads[sessionID] = uploadCtx
	sum.mu.Unlock()
	
	// 创建分片处理器（使用会话的分片大小）
	processor := NewChunkProcessor(sessionID, session.ChunkSize, config.MaxConcurrent)
	uploadCtx.Processor = processor

	log.Infof("Using chunk size %d bytes for session %s (file size: %d bytes)",
		session.ChunkSize, sessionID, session.FileSize)
	
	log.Infof("Starting streaming upload for session %s, file size: %d bytes",
		sessionID, session.FileSize)

	// 临时设置调试级别
	log.SetLevel(log.DebugLevel)
	log.Infof("Debug logging enabled for streaming upload")
	
	// 启动上传协程
	go func() {
		defer func() {
			sum.mu.Lock()
			delete(sum.activeUploads, sessionID)
			sum.mu.Unlock()
			cancel()
		}()
		
		uploadCtx.mu.Lock()
		uploadCtx.Status = StatusProcessing
		uploadCtx.mu.Unlock()
		
		// 执行流式上传
		log.Infof("Starting stream processing for session %s", sessionID)
		log.Debugf("Stream processing config: chunk_size=%d, max_concurrent=%d, enable_retry=%t",
			session.ChunkSize, config.MaxConcurrent, config.EnableRetry)
		err := sum.performStreamingUpload(ctx, processor, reader, config)

		uploadCtx.mu.Lock()
		if err != nil {
			uploadCtx.Status = StatusFailed
			uploadCtx.Error = err
			log.Errorf("Streaming upload failed for session %s: %v", sessionID, err)
		} else {
			uploadCtx.Status = StatusCompleted
			log.Infof("Streaming upload completed for session %s", sessionID)

			// 获取最终指标
			metrics := processor.GetMetrics()
			log.Infof("Final metrics for session %s: processed=%d/%d chunks, bytes=%d/%d",
				sessionID, metrics.ProcessedChunks, metrics.TotalChunks,
				metrics.ProcessedBytes, metrics.TotalBytes)
		}
		uploadCtx.mu.Unlock()
	}()
	
	return nil
}

// performStreamingUpload 执行流式上传
func (sum *StreamingUploadManager) performStreamingUpload(ctx context.Context,
	processor *ChunkProcessor, reader io.Reader, config *UploadConfig) error {

	log.Debugf("performStreamingUpload started")

	// 定义分片处理函数
	chunkProcessor := func(task *ChunkTask) error {
		log.Debugf("Processing chunk task: index=%d, size=%d", task.Index, task.Size)
		if config.EnableRetry {
			// 使用重试机制
			return sum.retryPolicy.Execute(ctx, func() error {
				return sum.processChunk(task)
			})
		} else {
			// 直接处理
			return sum.processChunk(task)
		}
	}

	// 开始流式处理
	log.Debugf("Starting ProcessStream with reader")
	err := processor.ProcessStream(ctx, reader, chunkProcessor)
	log.Debugf("ProcessStream completed with error: %v", err)
	return err
}

// processChunk 处理单个分片
func (sum *StreamingUploadManager) processChunk(task *ChunkTask) error {
	log.Debugf("Processing chunk %d, size: %d bytes, checksum: %s",
		task.Index, task.Size, task.Checksum)

	// 这里应该实现实际的分片上传逻辑
	// 例如：上传到云存储、本地文件系统等

	// 模拟处理时间（减少延迟以便测试）
	time.Sleep(50 * time.Millisecond)

	// 模拟偶尔的失败（用于测试重试机制）
	if task.Index%10 == 7 && task.Retries == 0 {
		task.Retries++
		return fmt.Errorf("simulated chunk processing failure (will retry)")
	}

	log.Debugf("Successfully processed chunk %d", task.Index)
	return nil
}

// CancelUpload 取消上传
func (sum *StreamingUploadManager) CancelUpload(sessionID string) error {
	sum.mu.Lock()
	defer sum.mu.Unlock()
	
	uploadCtx, exists := sum.activeUploads[sessionID]
	if !exists {
		return fmt.Errorf("no active upload found for session %s", sessionID)
	}
	
	uploadCtx.mu.Lock()
	uploadCtx.Status = StatusCancelled
	uploadCtx.mu.Unlock()
	
	uploadCtx.Cancel()
	
	log.Infof("Cancelled streaming upload for session %s", sessionID)
	return nil
}

// GetUploadStatus 获取上传状态
func (sum *StreamingUploadManager) GetUploadStatus(sessionID string) (*UploadContext, error) {
	sum.mu.RLock()
	defer sum.mu.RUnlock()
	
	uploadCtx, exists := sum.activeUploads[sessionID]
	if !exists {
		return nil, fmt.Errorf("no active upload found for session %s", sessionID)
	}
	
	uploadCtx.mu.RLock()
	defer uploadCtx.mu.RUnlock()
	
	// 返回副本
	return &UploadContext{
		SessionID: uploadCtx.SessionID,
		StartTime: uploadCtx.StartTime,
		Status:    uploadCtx.Status,
		Error:     uploadCtx.Error,
	}, nil
}

// GetActiveUploads 获取所有活跃上传
func (sum *StreamingUploadManager) GetActiveUploads() map[string]*UploadContext {
	sum.mu.RLock()
	defer sum.mu.RUnlock()
	
	result := make(map[string]*UploadContext)
	for sessionID, uploadCtx := range sum.activeUploads {
		uploadCtx.mu.RLock()
		result[sessionID] = &UploadContext{
			SessionID: uploadCtx.SessionID,
			StartTime: uploadCtx.StartTime,
			Status:    uploadCtx.Status,
			Error:     uploadCtx.Error,
		}
		uploadCtx.mu.RUnlock()
	}
	
	return result
}

// GetUploadMetrics 获取上传指标
func (sum *StreamingUploadManager) GetUploadMetrics(sessionID string) (*ProcessorMetrics, error) {
	sum.mu.RLock()
	defer sum.mu.RUnlock()
	
	uploadCtx, exists := sum.activeUploads[sessionID]
	if !exists {
		return nil, fmt.Errorf("no active upload found for session %s", sessionID)
	}
	
	if uploadCtx.Processor == nil {
		return nil, fmt.Errorf("processor not initialized for session %s", sessionID)
	}
	
	return uploadCtx.Processor.GetMetrics(), nil
}

// GetUploadProgress 获取上传进度
func (sum *StreamingUploadManager) GetUploadProgress(sessionID string) (float64, error) {
	sum.mu.RLock()
	defer sum.mu.RUnlock()
	
	uploadCtx, exists := sum.activeUploads[sessionID]
	if !exists {
		return 0, fmt.Errorf("no active upload found for session %s", sessionID)
	}
	
	if uploadCtx.Processor == nil {
		return 0, fmt.Errorf("processor not initialized for session %s", sessionID)
	}
	
	return uploadCtx.Processor.GetProgress(), nil
}

// GetUploadThroughput 获取上传吞吐量
func (sum *StreamingUploadManager) GetUploadThroughput(sessionID string) (float64, error) {
	sum.mu.RLock()
	defer sum.mu.RUnlock()
	
	uploadCtx, exists := sum.activeUploads[sessionID]
	if !exists {
		return 0, fmt.Errorf("no active upload found for session %s", sessionID)
	}
	
	if uploadCtx.Processor == nil {
		return 0, fmt.Errorf("processor not initialized for session %s", sessionID)
	}
	
	return uploadCtx.Processor.GetThroughput(), nil
}

// CleanupCompletedUploads 清理已完成的上传
func (sum *StreamingUploadManager) CleanupCompletedUploads() {
	sum.mu.Lock()
	defer sum.mu.Unlock()
	
	for sessionID, uploadCtx := range sum.activeUploads {
		uploadCtx.mu.RLock()
		status := uploadCtx.Status
		uploadCtx.mu.RUnlock()
		
		if status == StatusCompleted || status == StatusFailed || status == StatusCancelled {
			delete(sum.activeUploads, sessionID)
			log.Debugf("Cleaned up upload context for session %s", sessionID)
		}
	}
}

// 全局流式上传管理器
var GlobalStreamingUploadManager *StreamingUploadManager

// InitStreamingUploadManager 初始化流式上传管理器
func InitStreamingUploadManager() {
	log.Info("Initializing streaming upload manager...")
	GlobalStreamingUploadManager = NewStreamingUploadManager()

	if GlobalStreamingUploadManager == nil {
		log.Error("Failed to create streaming upload manager")
		return
	}

	// 启动清理协程
	go func() {
		ticker := time.NewTicker(5 * time.Minute)
		defer ticker.Stop()

		for range ticker.C {
			GlobalStreamingUploadManager.CleanupCompletedUploads()
		}
	}()

	log.Info("Streaming upload manager initialized successfully")
}
