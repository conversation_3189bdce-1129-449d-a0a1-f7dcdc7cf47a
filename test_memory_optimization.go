package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"
)

type BufferPoolStatus struct {
	Status            string            `json:"status"`
	CurrentMemoryMB   int64             `json:"current_memory_mb"`
	MaxMemoryMB       int64             `json:"max_memory_mb"`
	TotalAllocated    int64             `json:"total_allocated"`
	TotalReleased     int64             `json:"total_released"`
	CurrentInUse      int64             `json:"current_in_use"`
	PeakMemoryMB      int64             `json:"peak_memory_mb"`
	PoolHitRate       float64           `json:"pool_hit_rate"`
	SizeDistribution  map[string]int64  `json:"size_distribution"`
}

type RetryTestResult struct {
	Attempts int    `json:"attempts"`
	Duration string `json:"duration"`
	Success  bool   `json:"success"`
	Error    string `json:"error,omitempty"`
}

func main() {
	fmt.Println("🧪 内存优化和重试机制综合测试...")
	
	baseURL := "http://localhost:5244"
	
	// 测试1: 检查缓冲区池初始状态
	fmt.Println("\n📊 测试1: 检查缓冲区池初始状态...")
	checkBufferPoolStatus(baseURL)
	
	// 测试2: 并发测试缓冲区池（模拟内存使用）
	fmt.Println("\n🔥 测试2: 并发测试缓冲区池...")
	testConcurrentBufferUsage(baseURL)
	
	// 测试3: 重试机制测试
	fmt.Println("\n🔄 测试3: 重试机制测试...")
	testRetryMechanism(baseURL)
	
	// 测试4: 检查最终状态
	fmt.Println("\n📊 测试4: 检查最终状态...")
	checkBufferPoolStatus(baseURL)
	
	fmt.Println("\n🎉 综合测试完成！")
}

func checkBufferPoolStatus(baseURL string) {
	resp, err := http.Get(baseURL + "/api/buffer-pool/status")
	if err != nil {
		fmt.Printf("❌ 获取缓冲区池状态失败: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	body, _ := io.ReadAll(resp.Body)
	
	var apiResp struct {
		Code    int              `json:"code"`
		Message string           `json:"message"`
		Data    BufferPoolStatus `json:"data"`
	}
	
	if err := json.Unmarshal(body, &apiResp); err != nil {
		fmt.Printf("❌ 解析响应失败: %v\n", err)
		return
	}
	
	if apiResp.Code != 200 {
		fmt.Printf("❌ API错误: %s\n", apiResp.Message)
		return
	}
	
	status := apiResp.Data
	fmt.Printf("📊 缓冲区池状态:\n")
	fmt.Printf("  状态: %s\n", status.Status)
	fmt.Printf("  当前内存: %d MB / %d MB\n", status.CurrentMemoryMB, status.MaxMemoryMB)
	fmt.Printf("  总分配: %d, 总释放: %d, 当前使用: %d\n", 
		status.TotalAllocated, status.TotalReleased, status.CurrentInUse)
	fmt.Printf("  峰值内存: %d MB\n", status.PeakMemoryMB)
	fmt.Printf("  池命中率: %.2f%%\n", status.PoolHitRate)
	
	if len(status.SizeDistribution) > 0 {
		fmt.Printf("  大小分布: %v\n", status.SizeDistribution)
	}
}

func testConcurrentBufferUsage(baseURL string) {
	// 通过并发上传测试来间接测试缓冲区池
	var wg sync.WaitGroup
	numConcurrent := 20
	
	fmt.Printf("🚀 启动 %d 个并发上传测试...\n", numConcurrent)
	
	startTime := time.Now()
	
	for i := 0; i < numConcurrent; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			url := fmt.Sprintf("%s/api/upload/test?user_id=user%d&duration=500ms", baseURL, id%5)
			
			resp, err := http.Get(url)
			if err != nil {
				fmt.Printf("❌ 上传测试 %d 失败: %v\n", id, err)
				return
			}
			resp.Body.Close()
			
			fmt.Printf("✅ 上传测试 %d 完成\n", id)
		}(i)
	}
	
	wg.Wait()
	duration := time.Since(startTime)
	
	fmt.Printf("📈 并发测试完成，耗时: %v\n", duration)
}

func testRetryMechanism(baseURL string) {
	testCases := []struct {
		name        string
		failureRate string
		maxRetries  string
		expectSuccess bool
	}{
		{"成功案例", "0.0", "3", true},
		{"部分失败后成功", "0.5", "3", true},
		{"完全失败", "1.0", "3", false},
	}
	
	for _, tc := range testCases {
		fmt.Printf("🔄 测试重试机制: %s...\n", tc.name)
		
		url := fmt.Sprintf("%s/api/retry/test?failure_rate=%s&max_retries=%s", 
			baseURL, tc.failureRate, tc.maxRetries)
		
		resp, err := http.Get(url)
		if err != nil {
			fmt.Printf("❌ 重试测试失败: %v\n", err)
			continue
		}
		defer resp.Body.Close()
		
		body, _ := io.ReadAll(resp.Body)
		
		var apiResp struct {
			Code    int             `json:"code"`
			Message string          `json:"message"`
			Data    RetryTestResult `json:"data"`
		}
		
		if err := json.Unmarshal(body, &apiResp); err != nil {
			fmt.Printf("❌ 解析重试测试响应失败: %v\n", err)
			continue
		}
		
		result := apiResp.Data
		fmt.Printf("  尝试次数: %d\n", result.Attempts)
		fmt.Printf("  耗时: %s\n", result.Duration)
		fmt.Printf("  成功: %t\n", result.Success)
		
		if result.Error != "" {
			fmt.Printf("  错误: %s\n", result.Error)
		}
		
		if result.Success == tc.expectSuccess {
			fmt.Printf("✅ 测试 '%s' 通过\n", tc.name)
		} else {
			fmt.Printf("❌ 测试 '%s' 失败: 期望成功=%t, 实际成功=%t\n", 
				tc.name, tc.expectSuccess, result.Success)
		}
	}
}

func testMemoryCleanup(baseURL string) {
	fmt.Println("🧹 测试内存清理...")
	
	resp, err := http.Post(baseURL+"/api/buffer-pool/cleanup", "application/json", nil)
	if err != nil {
		fmt.Printf("❌ 内存清理失败: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	if resp.StatusCode == 200 {
		fmt.Println("✅ 内存清理成功")
	} else {
		fmt.Printf("❌ 内存清理失败，状态码: %d\n", resp.StatusCode)
	}
}
