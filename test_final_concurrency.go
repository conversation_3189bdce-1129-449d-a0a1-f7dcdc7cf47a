package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"
)

type TestResponse struct {
	Status   string `json:"status"`
	UserID   string `json:"user_id"`
	Duration string `json:"duration"`
	Message  string `json:"message"`
	Error    string `json:"error"`
}

func main() {
	fmt.Println("🚀 最终并发控制测试...")
	
	baseURL := "http://localhost:5244"
	
	// 测试1: 检查初始状态
	fmt.Println("📊 检查初始状态...")
	checkMetrics(baseURL)
	
	// 测试2: 单用户并发测试（超出用户限制）
	fmt.Println("\n🔥 测试单用户并发（8个并发，超出用户限制5个）...")
	testSingleUserConcurrency(baseURL, "user1", 8, "500ms")
	
	time.Sleep(1 * time.Second)
	
	// 测试3: 多用户并发测试（超出全局限制）
	fmt.Println("\n🌍 测试多用户并发（60个并发，超出全局限制50个）...")
	testMultiUserConcurrency(baseURL, 60, "200ms")
	
	time.Sleep(1 * time.Second)
	
	// 测试4: 检查最终状态
	fmt.Println("\n📊 检查最终状态...")
	checkMetrics(baseURL)
	
	fmt.Println("\n🎉 测试完成！")
}

func checkMetrics(baseURL string) {
	resp, err := http.Get(baseURL + "/api/upload/metrics")
	if err != nil {
		fmt.Printf("❌ 获取指标失败: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	body, _ := io.ReadAll(resp.Body)
	fmt.Printf("📊 当前指标: %s\n", string(body))
}

func testSingleUserConcurrency(baseURL, userID string, numRequests int, duration string) {
	var wg sync.WaitGroup
	results := make(chan TestResponse, numRequests)
	
	startTime := time.Now()
	
	for i := 0; i < numRequests; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			url := fmt.Sprintf("%s/api/upload/test?user_id=%s&duration=%s", baseURL, userID, duration)
			result := makeRequest(url)
			results <- result
		}(i)
	}
	
	wg.Wait()
	close(results)
	
	elapsed := time.Since(startTime)
	
	// 统计结果
	successCount := 0
	limitedCount := 0
	errorCount := 0
	
	fmt.Printf("📈 单用户测试结果 (用户: %s, 耗时: %v):\n", userID, elapsed)
	for result := range results {
		switch result.Status {
		case "success":
			successCount++
		case "limit_reached":
			limitedCount++
		default:
			errorCount++
		}
	}
	
	fmt.Printf("  ✅ 成功: %d\n", successCount)
	fmt.Printf("  🚫 限制: %d\n", limitedCount)
	fmt.Printf("  ❌ 错误: %d\n", errorCount)
	
	if limitedCount > 0 {
		fmt.Printf("🎯 用户级并发控制正常工作！限制了 %d 个请求\n", limitedCount)
	} else {
		fmt.Printf("⚠️  用户级并发控制未触发\n")
	}
}

func testMultiUserConcurrency(baseURL string, numRequests int, duration string) {
	var wg sync.WaitGroup
	results := make(chan TestResponse, numRequests)
	
	startTime := time.Now()
	
	for i := 0; i < numRequests; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			userID := fmt.Sprintf("user%d", id%10) // 10个不同用户
			url := fmt.Sprintf("%s/api/upload/test?user_id=%s&duration=%s", baseURL, userID, duration)
			result := makeRequest(url)
			results <- result
		}(i)
	}
	
	wg.Wait()
	close(results)
	
	elapsed := time.Since(startTime)
	
	// 统计结果
	successCount := 0
	limitedCount := 0
	errorCount := 0
	userStats := make(map[string]int)
	
	fmt.Printf("📈 多用户测试结果 (10个用户, 耗时: %v):\n", elapsed)
	for result := range results {
		switch result.Status {
		case "success":
			successCount++
			userStats[result.UserID]++
		case "limit_reached":
			limitedCount++
		default:
			errorCount++
		}
	}
	
	fmt.Printf("  ✅ 成功: %d\n", successCount)
	fmt.Printf("  🚫 限制: %d\n", limitedCount)
	fmt.Printf("  ❌ 错误: %d\n", errorCount)
	
	fmt.Printf("👥 用户分布:\n")
	for userID, count := range userStats {
		fmt.Printf("  %s: %d 个成功请求\n", userID, count)
	}
	
	if limitedCount > 0 {
		fmt.Printf("🎯 全局并发控制正常工作！限制了 %d 个请求\n", limitedCount)
	} else {
		fmt.Printf("⚠️  全局并发控制未触发\n")
	}
}

func makeRequest(url string) TestResponse {
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Get(url)
	if err != nil {
		return TestResponse{
			Status: "error",
			Error:  fmt.Sprintf("请求失败: %v", err),
		}
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return TestResponse{
			Status: "error",
			Error:  fmt.Sprintf("读取响应失败: %v", err),
		}
	}
	
	var result TestResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return TestResponse{
			Status: "error",
			Error:  fmt.Sprintf("解析JSON失败: %v", err),
		}
	}
	
	return result
}
