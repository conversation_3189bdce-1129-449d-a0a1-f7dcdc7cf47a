package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

func main() {
	fmt.Println("🔍 流式上传集成诊断工具...")
	
	baseURL := "http://localhost:5244"
	userID := "debug_user"
	
	// 步骤1: 创建上传会话
	fmt.Println("\n📝 步骤1: 创建上传会话...")
	sessionID, err := createDebugSession(baseURL, userID)
	if err != nil {
		fmt.Printf("❌ 创建会话失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 会话创建成功: %s\n", sessionID)
	
	// 步骤2: 检查会话详情
	fmt.Println("\n📊 步骤2: 检查会话详情...")
	err = checkSessionDetails(baseURL, sessionID)
	if err != nil {
		fmt.Printf("❌ 检查会话失败: %v\n", err)
		return
	}
	
	// 步骤3: 测试小数据流式上传
	fmt.Println("\n🧪 步骤3: 测试小数据流式上传...")
	testData := []byte("Hello, World! This is a test data for streaming upload integration.")
	err = testSmallStreamingUpload(baseURL, sessionID, testData)
	if err != nil {
		fmt.Printf("❌ 小数据上传失败: %v\n", err)
		return
	}
	
	// 步骤4: 监控进度变化
	fmt.Println("\n📊 步骤4: 监控进度变化...")
	err = monitorProgressChanges(baseURL, sessionID, 10*time.Second)
	if err != nil {
		fmt.Printf("❌ 监控失败: %v\n", err)
		return
	}
	
	// 步骤5: 检查流式上传状态
	fmt.Println("\n🔍 步骤5: 检查流式上传状态...")
	err = checkStreamingStatus(baseURL, sessionID)
	if err != nil {
		fmt.Printf("❌ 检查流式状态失败: %v\n", err)
	}
	
	fmt.Println("\n🎯 诊断完成！")
}

func createDebugSession(baseURL, userID string) (string, error) {
	reqData := map[string]interface{}{
		"file_name":  "debug_test.txt",
		"file_path":  "/debug/debug_test.txt",
		"file_size":  64,  // 非常小的文件
		"chunk_size": 32,  // 非常小的分片
		"metadata": map[string]string{
			"test_type": "debug",
		},
	}
	
	jsonData, _ := json.Marshal(reqData)
	
	req, err := http.NewRequest("POST", baseURL+"/api/resumable/upload", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", err
	}
	
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-User-ID", userID)
	
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	
	fmt.Printf("📄 创建会话响应: %s\n", string(body))
	
	var apiResp map[string]interface{}
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return "", err
	}
	
	if code, ok := apiResp["code"].(float64); !ok || code != 200 {
		return "", fmt.Errorf("API error: %v", apiResp["message"])
	}
	
	data := apiResp["data"].(map[string]interface{})
	return data["session_id"].(string), nil
}

func checkSessionDetails(baseURL, sessionID string) error {
	// 检查断点续传进度
	progressURL := fmt.Sprintf("%s/api/resumable/upload/%s/progress", baseURL, sessionID)
	resp, err := http.Get(progressURL)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	
	fmt.Printf("📊 断点续传进度: %s\n", string(body))
	
	// 检查缺失分片
	missingURL := fmt.Sprintf("%s/api/resumable/upload/%s/missing", baseURL, sessionID)
	resp, err = http.Get(missingURL)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	body, err = io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	
	fmt.Printf("🔍 缺失分片: %s\n", string(body))
	
	return nil
}

func testSmallStreamingUpload(baseURL, sessionID string, data []byte) error {
	fmt.Printf("📤 开始上传 %d 字节数据...\n", len(data))
	
	// 使用非常小的分片大小
	url := fmt.Sprintf("%s/api/streaming/upload/%s?chunk_size=32&max_concurrent=1&enable_retry=true", 
		baseURL, sessionID)
	
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(data))
	if err != nil {
		return err
	}
	
	req.Header.Set("Content-Type", "application/octet-stream")
	
	client := &http.Client{Timeout: 30 * time.Second}
	
	fmt.Printf("🚀 发送流式上传请求到: %s\n", url)
	
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	
	fmt.Printf("📄 流式上传响应 (状态: %d): %s\n", resp.StatusCode, string(body))
	
	if resp.StatusCode != 200 {
		return fmt.Errorf("upload failed with status %d: %s", resp.StatusCode, string(body))
	}
	
	return nil
}

func monitorProgressChanges(baseURL, sessionID string, duration time.Duration) error {
	startTime := time.Now()
	lastProgress := float64(-1)
	
	fmt.Printf("⏱️  监控进度变化 %v...\n", duration)
	
	for time.Since(startTime) < duration {
		progressURL := fmt.Sprintf("%s/api/resumable/upload/%s/progress", baseURL, sessionID)
		resp, err := http.Get(progressURL)
		if err != nil {
			fmt.Printf("⚠️  获取进度失败: %v\n", err)
			time.Sleep(1 * time.Second)
			continue
		}
		
		body, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			fmt.Printf("⚠️  读取响应失败: %v\n", err)
			time.Sleep(1 * time.Second)
			continue
		}
		
		var apiResp map[string]interface{}
		if err := json.Unmarshal(body, &apiResp); err != nil {
			fmt.Printf("⚠️  解析响应失败: %v\n", err)
			time.Sleep(1 * time.Second)
			continue
		}
		
		if code, ok := apiResp["code"].(float64); !ok || code != 200 {
			fmt.Printf("⚠️  API错误: %v\n", apiResp["message"])
			time.Sleep(1 * time.Second)
			continue
		}
		
		data := apiResp["data"].(map[string]interface{})
		progress := data["progress_percent"].(float64)
		completedChunks := data["completed_chunks"].(float64)
		totalChunks := data["total_chunks"].(float64)
		status := data["status"].(string)
		
		if progress != lastProgress {
			fmt.Printf("📊 进度变化: %.2f%% (%d/%d chunks), 状态: %s\n", 
				progress, int(completedChunks), int(totalChunks), status)
			lastProgress = progress
		}
		
		if status == "completed" {
			fmt.Printf("✅ 上传完成！\n")
			return nil
		}
		
		time.Sleep(500 * time.Millisecond)
	}
	
	fmt.Printf("⏰ 监控结束，最终进度: %.2f%%\n", lastProgress)
	return nil
}

func checkStreamingStatus(baseURL, sessionID string) error {
	// 检查活跃流式上传
	uploadsURL := fmt.Sprintf("%s/api/streaming/uploads", baseURL)
	resp, err := http.Get(uploadsURL)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	
	fmt.Printf("📋 活跃流式上传: %s\n", string(body))
	
	// 尝试获取特定会话的流式状态
	statusURL := fmt.Sprintf("%s/api/streaming/upload/%s/status", baseURL, sessionID)
	resp, err = http.Get(statusURL)
	if err != nil {
		fmt.Printf("⚠️  无法获取流式状态: %v\n", err)
		return nil
	}
	defer resp.Body.Close()
	
	body, err = io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	
	fmt.Printf("📊 流式上传状态: %s\n", string(body))
	
	return nil
}
