package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

type UploadSession struct {
	SessionID   string    `json:"session_id"`
	FileName    string    `json:"file_name"`
	FileSize    int64     `json:"file_size"`
	ChunkSize   int64     `json:"chunk_size"`
	TotalChunks int64     `json:"total_chunks"`
	CreatedAt   time.Time `json:"created_at"`
	ExpiresAt   time.Time `json:"expires_at"`
}

type UploadProgress struct {
	SessionID              string  `json:"session_id"`
	FileName               string  `json:"file_name"`
	FileSize               int64   `json:"file_size"`
	UploadedBytes          int64   `json:"uploaded_bytes"`
	TotalChunks            int64   `json:"total_chunks"`
	CompletedChunks        int64   `json:"completed_chunks"`
	ProgressPercent        float64 `json:"progress_percent"`
	Status                 string  `json:"status"`
	EstimatedTimeRemaining int64   `json:"estimated_time_remaining"`
}

func main() {
	fmt.Println("🧪 断点续传功能综合测试...")
	
	baseURL := "http://localhost:5244"
	userID := "test_user"
	
	// 测试1: 创建上传会话
	fmt.Println("\n📝 测试1: 创建上传会话...")
	sessionID, err := createUploadSession(baseURL, userID)
	if err != nil {
		fmt.Printf("❌ 创建上传会话失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 上传会话创建成功: %s\n", sessionID)
	
	// 测试2: 获取初始进度
	fmt.Println("\n📊 测试2: 获取初始进度...")
	progress, err := getUploadProgress(baseURL, sessionID)
	if err != nil {
		fmt.Printf("❌ 获取进度失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 初始进度: %.2f%% (%d/%d chunks)\n", 
		progress.ProgressPercent, progress.CompletedChunks, progress.TotalChunks)
	
	// 测试3: 上传分片
	fmt.Println("\n📤 测试3: 上传分片...")
	chunkData := make([]byte, 256)
	for i := range chunkData {
		chunkData[i] = byte(i % 256)
	}
	
	// 上传第0个分片
	err = uploadChunk(baseURL, sessionID, 0, chunkData)
	if err != nil {
		fmt.Printf("❌ 上传分片0失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 分片0上传成功\n")
	
	// 上传第2个分片（跳过第1个，模拟断点续传）
	err = uploadChunk(baseURL, sessionID, 2, chunkData)
	if err != nil {
		fmt.Printf("❌ 上传分片2失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 分片2上传成功\n")
	
	// 测试4: 获取缺失分片
	fmt.Println("\n🔍 测试4: 获取缺失分片...")
	missingChunks, err := getMissingChunks(baseURL, sessionID)
	if err != nil {
		fmt.Printf("❌ 获取缺失分片失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 缺失分片: %v\n", missingChunks)
	
	// 测试5: 恢复上传会话
	fmt.Println("\n🔄 测试5: 恢复上传会话...")
	resumeInfo, err := resumeUpload(baseURL, sessionID)
	if err != nil {
		fmt.Printf("❌ 恢复上传失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 上传会话恢复成功，缺失分片: %d个\n", len(resumeInfo))
	
	// 测试6: 完成剩余分片上传
	fmt.Println("\n📤 测试6: 完成剩余分片上传...")
	for _, chunkIndex := range missingChunks {
		err = uploadChunk(baseURL, sessionID, chunkIndex, chunkData)
		if err != nil {
			fmt.Printf("❌ 上传分片%d失败: %v\n", chunkIndex, err)
			return
		}
		fmt.Printf("✅ 分片%d上传成功\n", chunkIndex)
	}
	
	// 测试7: 获取最终进度
	fmt.Println("\n📊 测试7: 获取最终进度...")
	finalProgress, err := getUploadProgress(baseURL, sessionID)
	if err != nil {
		fmt.Printf("❌ 获取最终进度失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 最终进度: %.2f%% (%d/%d chunks), 状态: %s\n", 
		finalProgress.ProgressPercent, finalProgress.CompletedChunks, 
		finalProgress.TotalChunks, finalProgress.Status)
	
	// 测试8: 列出用户上传
	fmt.Println("\n📋 测试8: 列出用户上传...")
	uploads, err := listUserUploads(baseURL, userID)
	if err != nil {
		fmt.Printf("❌ 列出用户上传失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 用户上传列表: %d个会话\n", len(uploads))
	
	// 测试9: 获取上传状态统计
	fmt.Println("\n📈 测试9: 获取上传状态统计...")
	stats, err := getUploadStats(baseURL)
	if err != nil {
		fmt.Printf("❌ 获取统计失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 统计信息: %v\n", stats)
	
	fmt.Println("\n🎉 断点续传功能测试完成！")
}

func createUploadSession(baseURL, userID string) (string, error) {
	reqData := map[string]interface{}{
		"file_name": "test_file.txt",
		"file_path": "/uploads/test_file.txt",
		"file_size": 1024,
		"chunk_size": 256,
		"metadata": map[string]string{
			"content_type": "text/plain",
		},
	}
	
	jsonData, _ := json.Marshal(reqData)
	
	req, err := http.NewRequest("POST", baseURL+"/api/resumable/upload", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", err
	}
	
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-User-ID", userID)
	
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return "", err
	}
	
	if apiResp.Code != 200 {
		return "", fmt.Errorf("API error: %s", apiResp.Message)
	}
	
	data := apiResp.Data.(map[string]interface{})
	return data["session_id"].(string), nil
}

func uploadChunk(baseURL, sessionID string, chunkIndex int64, chunkData []byte) error {
	url := fmt.Sprintf("%s/api/resumable/upload/%s/chunk/%d", baseURL, sessionID, chunkIndex)
	
	req, err := http.NewRequest("PUT", url, bytes.NewBuffer(chunkData))
	if err != nil {
		return err
	}
	
	req.Header.Set("Content-Type", "application/octet-stream")
	
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("upload failed: %s", string(body))
	}
	
	return nil
}

func getUploadProgress(baseURL, sessionID string) (*UploadProgress, error) {
	url := fmt.Sprintf("%s/api/resumable/upload/%s/progress", baseURL, sessionID)
	
	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, err
	}
	
	if apiResp.Code != 200 {
		return nil, fmt.Errorf("API error: %s", apiResp.Message)
	}
	
	progressData, _ := json.Marshal(apiResp.Data)
	var progress UploadProgress
	if err := json.Unmarshal(progressData, &progress); err != nil {
		return nil, err
	}
	
	return &progress, nil
}

func getMissingChunks(baseURL, sessionID string) ([]int64, error) {
	url := fmt.Sprintf("%s/api/resumable/upload/%s/missing", baseURL, sessionID)
	
	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, err
	}
	
	if apiResp.Code != 200 {
		return nil, fmt.Errorf("API error: %s", apiResp.Message)
	}
	
	data := apiResp.Data.(map[string]interface{})
	missingInterface := data["missing_chunks"].([]interface{})
	
	var missing []int64
	for _, v := range missingInterface {
		missing = append(missing, int64(v.(float64)))
	}
	
	return missing, nil
}

func resumeUpload(baseURL, sessionID string) ([]int64, error) {
	url := fmt.Sprintf("%s/api/resumable/upload/%s", baseURL, sessionID)
	
	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, err
	}
	
	if apiResp.Code != 200 {
		return nil, fmt.Errorf("API error: %s", apiResp.Message)
	}
	
	data := apiResp.Data.(map[string]interface{})
	missingInterface := data["missing_chunks"].([]interface{})
	
	var missing []int64
	for _, v := range missingInterface {
		missing = append(missing, int64(v.(float64)))
	}
	
	return missing, nil
}

func listUserUploads(baseURL, userID string) ([]interface{}, error) {
	req, err := http.NewRequest("GET", baseURL+"/api/resumable/uploads", nil)
	if err != nil {
		return nil, err
	}
	
	req.Header.Set("X-User-ID", userID)
	
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, err
	}
	
	if apiResp.Code != 200 {
		return nil, fmt.Errorf("API error: %s", apiResp.Message)
	}
	
	data := apiResp.Data.(map[string]interface{})
	sessions := data["sessions"].([]interface{})
	
	return sessions, nil
}

func getUploadStats(baseURL string) (map[string]interface{}, error) {
	resp, err := http.Get(baseURL + "/api/resumable/status")
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, err
	}
	
	if apiResp.Code != 200 {
		return nil, fmt.Errorf("API error: %s", apiResp.Message)
	}
	
	return apiResp.Data.(map[string]interface{}), nil
}
