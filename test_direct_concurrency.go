package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"
)

func main() {
	fmt.Println("🧪 直接并发控制测试...")
	
	baseURL := "http://localhost:5244"
	
	// 测试1: 快速连续请求同一用户（应该触发用户级限制）
	fmt.Println("⚡ 测试1: 快速连续请求同一用户...")
	testRapidRequests(baseURL, "user1", 10, "3s")
	
	time.Sleep(5 * time.Second) // 等待所有请求完成
	
	// 检查指标
	fmt.Println("\n📊 检查指标...")
	checkMetrics(baseURL)
	
	fmt.Println("\n🎉 测试完成！")
}

func testRapidRequests(baseURL, userID string, numRequests int, duration string) {
	var wg sync.WaitGroup
	results := make([]string, numRequests)
	
	fmt.Printf("🚀 启动 %d 个并发请求 (用户: %s, 持续时间: %s)...\n", numRequests, userID, duration)
	
	startTime := time.Now()
	
	for i := 0; i < numRequests; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()
			
			url := fmt.Sprintf("%s/api/upload/test?user_id=%s&duration=%s", baseURL, userID, duration)
			
			client := &http.Client{Timeout: 10 * time.Second}
			resp, err := client.Get(url)
			if err != nil {
				results[index] = fmt.Sprintf("请求 %d: 错误 - %v", index, err)
				return
			}
			defer resp.Body.Close()
			
			body, _ := io.ReadAll(resp.Body)
			
			var response map[string]interface{}
			json.Unmarshal(body, &response)
			
			if status, ok := response["status"]; ok {
				results[index] = fmt.Sprintf("请求 %d: %s", index, status)
			} else {
				results[index] = fmt.Sprintf("请求 %d: HTTP %d", index, resp.StatusCode)
			}
		}(i)
	}
	
	// 等待所有请求启动
	time.Sleep(100 * time.Millisecond)
	
	// 检查中间状态
	fmt.Println("📊 检查中间状态...")
	checkMetrics(baseURL)
	
	wg.Wait()
	elapsed := time.Since(startTime)
	
	fmt.Printf("\n📈 测试结果 (耗时: %v):\n", elapsed)
	
	successCount := 0
	limitCount := 0
	errorCount := 0
	
	for _, result := range results {
		fmt.Printf("  %s\n", result)
		
		if contains(result, "success") {
			successCount++
		} else if contains(result, "limit_reached") {
			limitCount++
		} else {
			errorCount++
		}
	}
	
	fmt.Printf("\n📊 统计:\n")
	fmt.Printf("  ✅ 成功: %d\n", successCount)
	fmt.Printf("  🚫 限制: %d\n", limitCount)
	fmt.Printf("  ❌ 错误: %d\n", errorCount)
	
	if limitCount > 0 {
		fmt.Printf("🎯 并发控制工作正常！成功限制了 %d 个请求\n", limitCount)
	} else if successCount == numRequests {
		fmt.Printf("⚠️  所有请求都成功了，可能并发限制设置过高或请求处理太快\n")
	} else {
		fmt.Printf("❓ 结果不明确，需要进一步调查\n")
	}
}

func checkMetrics(baseURL string) {
	resp, err := http.Get(baseURL + "/api/upload/metrics")
	if err != nil {
		fmt.Printf("❌ 获取指标失败: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	body, _ := io.ReadAll(resp.Body)
	
	var metrics map[string]interface{}
	if err := json.Unmarshal(body, &metrics); err != nil {
		fmt.Printf("❌ 解析指标失败: %v\n", err)
		return
	}
	
	if data, ok := metrics["data"].(map[string]interface{}); ok {
		fmt.Printf("📊 指标: 活跃=%v, 排队=%v, 拒绝=%v\n", 
			data["total_active_uploads"], 
			data["queued_uploads"], 
			data["rejected_uploads"])
	} else {
		fmt.Printf("📊 原始指标: %s\n", string(body))
	}
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && 
		(s == substr || 
		 (len(s) > len(substr) && 
		  (s[:len(substr)] == substr || 
		   s[len(s)-len(substr):] == substr)))
}
