package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

type StreamingConfig struct {
	DefaultConfig struct {
		ChunkSize     int64  `json:"chunk_size"`
		MaxConcurrent int    `json:"max_concurrent"`
		EnableRetry   bool   `json:"enable_retry"`
		MaxRetries    int    `json:"max_retries"`
		Timeout       string `json:"timeout"`
	} `json:"default_config"`
	Limits struct {
		MinChunkSize    int64  `json:"min_chunk_size"`
		MaxChunkSize    int64  `json:"max_chunk_size"`
		MinConcurrent   int    `json:"min_concurrent"`
		MaxConcurrent   int    `json:"max_concurrent"`
		MinTimeout      string `json:"min_timeout"`
		MaxTimeout      string `json:"max_timeout"`
	} `json:"limits"`
}

type StreamingStatus struct {
	SessionID  string  `json:"session_id"`
	Status     string  `json:"status"`
	StartedAt  string  `json:"started_at"`
	Progress   float64 `json:"progress"`
	Throughput float64 `json:"throughput"`
	Error      string  `json:"error,omitempty"`
}

func main() {
	fmt.Println("🧪 流式分片上传功能综合测试...")
	
	baseURL := "http://localhost:5244"
	userID := "test_user"
	
	// 测试1: 获取流式上传配置
	fmt.Println("\n⚙️  测试1: 获取流式上传配置...")
	config, err := getStreamingConfig(baseURL)
	if err != nil {
		fmt.Printf("❌ 获取配置失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 默认配置: 分片大小=%d, 并发数=%d, 重试=%t\n", 
		config.DefaultConfig.ChunkSize, config.DefaultConfig.MaxConcurrent, config.DefaultConfig.EnableRetry)
	
	// 测试2: 创建上传会话
	fmt.Println("\n📝 测试2: 创建上传会话...")
	sessionID, err := createUploadSession(baseURL, userID)
	if err != nil {
		fmt.Printf("❌ 创建上传会话失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 上传会话创建成功: %s\n", sessionID)
	
	// 测试3: 开始流式上传
	fmt.Println("\n🚀 测试3: 开始流式上传...")
	testData := generateTestData(1024 * 1024) // 1MB测试数据
	err = startStreamingUpload(baseURL, sessionID, testData)
	if err != nil {
		fmt.Printf("❌ 开始流式上传失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 流式上传已启动\n")
	
	// 测试4: 监控上传进度
	fmt.Println("\n📊 测试4: 监控上传进度...")
	err = monitorUploadProgress(baseURL, sessionID, 10*time.Second)
	if err != nil {
		fmt.Printf("❌ 监控上传进度失败: %v\n", err)
		return
	}
	
	// 测试5: 获取详细指标
	fmt.Println("\n📈 测试5: 获取详细指标...")
	metrics, err := getUploadMetrics(baseURL, sessionID)
	if err != nil {
		fmt.Printf("❌ 获取指标失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 上传指标: %v\n", metrics)
	
	// 测试6: 列出活跃上传
	fmt.Println("\n📋 测试6: 列出活跃上传...")
	activeUploads, err := listActiveUploads(baseURL)
	if err != nil {
		fmt.Printf("❌ 列出活跃上传失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 活跃上传数量: %d\n", len(activeUploads))
	
	fmt.Println("\n🎉 流式分片上传功能测试完成！")
}

func getStreamingConfig(baseURL string) (*StreamingConfig, error) {
	resp, err := http.Get(baseURL + "/api/streaming/config")
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, err
	}
	
	if apiResp.Code != 200 {
		return nil, fmt.Errorf("API error: %s", apiResp.Message)
	}
	
	configData, _ := json.Marshal(apiResp.Data)
	var config StreamingConfig
	if err := json.Unmarshal(configData, &config); err != nil {
		return nil, err
	}
	
	return &config, nil
}

func createUploadSession(baseURL, userID string) (string, error) {
	reqData := map[string]interface{}{
		"file_name": "streaming_test.txt",
		"file_path": "/uploads/streaming_test.txt",
		"file_size": 1024 * 1024, // 1MB
		"chunk_size": 256 * 1024,  // 256KB
		"metadata": map[string]string{
			"content_type": "text/plain",
			"upload_type":  "streaming",
		},
	}
	
	jsonData, _ := json.Marshal(reqData)
	
	req, err := http.NewRequest("POST", baseURL+"/api/resumable/upload", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", err
	}
	
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-User-ID", userID)
	
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return "", err
	}
	
	if apiResp.Code != 200 {
		return "", fmt.Errorf("API error: %s", apiResp.Message)
	}
	
	data := apiResp.Data.(map[string]interface{})
	return data["session_id"].(string), nil
}

func startStreamingUpload(baseURL, sessionID string, data []byte) error {
	url := fmt.Sprintf("%s/api/streaming/upload/%s?chunk_size=262144&max_concurrent=2", baseURL, sessionID)
	
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(data))
	if err != nil {
		return err
	}
	
	req.Header.Set("Content-Type", "application/octet-stream")
	
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("upload failed: %s", string(body))
	}
	
	return nil
}

func monitorUploadProgress(baseURL, sessionID string, timeout time.Duration) error {
	startTime := time.Now()
	
	for time.Since(startTime) < timeout {
		status, err := getUploadStatus(baseURL, sessionID)
		if err != nil {
			// 如果上传已完成，可能会找不到活跃上传
			if time.Since(startTime) > 5*time.Second {
				fmt.Printf("✅ 上传可能已完成（无法获取状态）\n")
				return nil
			}
			time.Sleep(500 * time.Millisecond)
			continue
		}
		
		fmt.Printf("📊 状态: %s, 进度: %.2f%%, 吞吐量: %.2f MB/s\n", 
			status.Status, status.Progress, status.Throughput/(1024*1024))
		
		if status.Status == "completed" {
			fmt.Printf("✅ 上传完成！\n")
			return nil
		}
		
		if status.Status == "failed" {
			return fmt.Errorf("upload failed: %s", status.Error)
		}
		
		time.Sleep(1 * time.Second)
	}
	
	return fmt.Errorf("monitoring timeout after %v", timeout)
}

func getUploadStatus(baseURL, sessionID string) (*StreamingStatus, error) {
	url := fmt.Sprintf("%s/api/streaming/upload/%s/status", baseURL, sessionID)
	
	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, err
	}
	
	if apiResp.Code != 200 {
		return nil, fmt.Errorf("API error: %s", apiResp.Message)
	}
	
	statusData, _ := json.Marshal(apiResp.Data)
	var status StreamingStatus
	if err := json.Unmarshal(statusData, &status); err != nil {
		return nil, err
	}
	
	return &status, nil
}

func getUploadMetrics(baseURL, sessionID string) (map[string]interface{}, error) {
	url := fmt.Sprintf("%s/api/streaming/upload/%s/metrics", baseURL, sessionID)
	
	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, err
	}
	
	if apiResp.Code != 200 {
		return nil, fmt.Errorf("API error: %s", apiResp.Message)
	}
	
	return apiResp.Data.(map[string]interface{}), nil
}

func listActiveUploads(baseURL string) ([]interface{}, error) {
	resp, err := http.Get(baseURL + "/api/streaming/uploads")
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, err
	}
	
	if apiResp.Code != 200 {
		return nil, fmt.Errorf("API error: %s", apiResp.Message)
	}
	
	data := apiResp.Data.(map[string]interface{})
	uploads := data["active_uploads"].([]interface{})
	
	return uploads, nil
}

func generateTestData(size int) []byte {
	data := make([]byte, size)
	for i := range data {
		data[i] = byte(i % 256)
	}
	return data
}
