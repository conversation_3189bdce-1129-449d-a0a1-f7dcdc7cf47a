# AList 上传优化快速参考

## 🚀 快速开始

### 编译和启动
```bash
# 编译
go build -o alist main.go

# 启动服务
./alist server --data ./data

# 验证功能
curl http://localhost:5244/api/upload/metrics
```

### 基础配置
```json
{
  "max_concurrent_uploads_per_user": 5,
  "max_concurrent_uploads_global": 50,
  "max_buffer_pool_memory_mb": 100
}
```

---

## 📊 核心API速查

### 并发控制
```bash
# 获取并发状态
GET /api/upload/metrics

# 测试并发控制
GET /api/upload/test?user_id=test&duration=5s
```

### 内存管理
```bash
# 缓冲区池状态
GET /api/buffer-pool/status

# 清理内存
POST /api/buffer-pool/cleanup

# 增强指标
GET /api/upload/enhanced-metrics
```

### 断点续传
```bash
# 创建上传会话
POST /api/resumable/upload
{
  "file_name": "test.txt",
  "file_path": "/uploads/test.txt",
  "file_size": 1048576,
  "chunk_size": 262144
}

# 上传分片
PUT /api/resumable/upload/{session_id}/chunk/{chunk_index}

# 获取进度
GET /api/resumable/upload/{session_id}/progress

# 恢复上传
GET /api/resumable/upload/{session_id}
```

### 流式上传
```bash
# 开始流式上传
POST /api/streaming/upload/{session_id}?chunk_size=131072&max_concurrent=2

# 获取状态
GET /api/streaming/upload/{session_id}/status

# 获取指标
GET /api/streaming/upload/{session_id}/metrics

# 列出活跃上传
GET /api/streaming/uploads
```

---

## 🔧 常用测试命令

### 1. 完整流程测试
```bash
# 创建会话
SESSION_ID=$(curl -s -X POST http://localhost:5244/api/resumable/upload \
  -H "Content-Type: application/json" \
  -H "X-User-ID: test_user" \
  -d '{
    "file_name": "test.txt",
    "file_path": "/test.txt",
    "file_size": 1024,
    "chunk_size": 256
  }' | jq -r '.data.session_id')

# 开始流式上传
echo "Hello World Test Data" | curl -X POST \
  "http://localhost:5244/api/streaming/upload/$SESSION_ID?chunk_size=256&max_concurrent=1" \
  -H "Content-Type: application/octet-stream" \
  --data-binary @-

# 检查进度
curl -s "http://localhost:5244/api/resumable/upload/$SESSION_ID/progress"
```

### 2. 并发压力测试
```bash
# 启动多个并发测试
for i in {1..10}; do
  curl -s "http://localhost:5244/api/upload/test?user_id=user_$i&duration=5s" &
done
wait

# 检查并发状态
curl -s http://localhost:5244/api/upload/metrics
```

### 3. 内存压力测试
```bash
# 检查初始内存状态
curl -s http://localhost:5244/api/buffer-pool/status

# 创建多个大文件上传会话
for i in {1..5}; do
  curl -s -X POST http://localhost:5244/api/resumable/upload \
    -H "Content-Type: application/json" \
    -H "X-User-ID: user_$i" \
    -d "{
      \"file_name\": \"large_file_$i.txt\",
      \"file_path\": \"/large_file_$i.txt\",
      \"file_size\": 10485760,
      \"chunk_size\": 1048576
    }"
done

# 检查内存使用
curl -s http://localhost:5244/api/buffer-pool/status
```

---

## 🐛 故障排查

### 常见错误码
```
400: 请求参数错误
404: 会话不存在或已过期
429: 并发限制超出
500: 服务器内部错误
503: 服务不可用
```

### 快速诊断
```bash
# 检查系统整体状态
curl -s http://localhost:5244/api/upload/metrics
curl -s http://localhost:5244/api/buffer-pool/status
curl -s http://localhost:5244/api/streaming/uploads

# 检查特定会话
curl -s http://localhost:5244/api/resumable/upload/{session_id}/progress
curl -s http://localhost:5244/api/streaming/upload/{session_id}/status
```

### 清理操作
```bash
# 清理内存
curl -X POST http://localhost:5244/api/buffer-pool/cleanup

# 取消上传
curl -X POST http://localhost:5244/api/resumable/upload/{session_id}/cancel
curl -X POST http://localhost:5244/api/streaming/upload/{session_id}/cancel

# 删除会话
curl -X DELETE http://localhost:5244/api/resumable/upload/{session_id}
```

---

## 📈 性能监控

### 关键指标
```bash
# 并发使用情况
curl -s http://localhost:5244/api/upload/metrics | jq '.data'

# 内存使用情况
curl -s http://localhost:5244/api/buffer-pool/status | jq '.data'

# 上传活动
curl -s http://localhost:5244/api/streaming/uploads | jq '.data'
```

### 性能基准
```
并发控制:
- 用户并发: ≤ 5/用户
- 全局并发: ≤ 50
- 响应时间: < 100ms

内存管理:
- 内存使用: ≤ 100MB
- 池命中率: > 80%
- GC频率: 正常

上传性能:
- 吞吐量: > 100MB/s
- 错误率: < 5%
- 恢复率: > 85%
```

---

## 🔧 配置调优

### 高并发场景
```json
{
  "max_concurrent_uploads_per_user": 10,
  "max_concurrent_uploads_global": 100,
  "max_buffer_pool_memory_mb": 200
}
```

### 大文件场景
```json
{
  "max_concurrent_uploads_per_user": 3,
  "max_concurrent_uploads_global": 30,
  "max_buffer_pool_memory_mb": 500
}
```

### 内存受限场景
```json
{
  "max_concurrent_uploads_per_user": 2,
  "max_concurrent_uploads_global": 20,
  "max_buffer_pool_memory_mb": 50
}
```

---

## 📝 开发指南

### 添加新的重试策略
```go
// 创建自定义重试策略
customPolicy := &retry.RetryPolicy{
    MaxRetries:    5,
    BaseDelay:     200 * time.Millisecond,
    MaxDelay:      30 * time.Second,
    BackoffFactor: 2.0,
    Jitter:        0.1,
}

// 使用策略
err := customPolicy.Execute(ctx, func() error {
    return yourOperation()
})
```

### 自定义存储后端
```go
// 实现存储接口
type CustomStorage struct {
    // 你的存储实现
}

func (cs *CustomStorage) CreateSession(session *UploadSession) error {
    // 实现会话创建逻辑
    return nil
}

// 注册存储后端
resume.GlobalResumeStorage = &CustomStorage{}
```

### 添加监控指标
```go
// 添加自定义指标
type CustomMetrics struct {
    YourMetric int64 `json:"your_metric"`
}

// 在API中返回
func YourMetricsHandler(c *gin.Context) {
    metrics := &CustomMetrics{
        YourMetric: getYourMetric(),
    }
    common.SuccessResp(c, metrics)
}
```

---

## 🎯 最佳实践

### 1. 上传策略选择
- **小文件 (< 1MB)**: 直接上传
- **中等文件 (1-100MB)**: 断点续传
- **大文件 (> 100MB)**: 流式分片上传

### 2. 分片大小选择
- **网络良好**: 1-4MB分片
- **网络一般**: 256KB-1MB分片
- **网络较差**: 64-256KB分片

### 3. 并发数选择
- **高性能服务器**: 4-8并发
- **普通服务器**: 2-4并发
- **资源受限**: 1-2并发

### 4. 错误处理
- **网络错误**: 自动重试
- **服务器错误**: 指数退避重试
- **客户端错误**: 立即失败
- **超时错误**: 增加超时时间后重试

---

## 📞 支持信息

### 文档链接
- [完整技术文档](./upload_optimization_summary.md)
- [API参考文档](./api_reference.md)
- [故障排查指南](./troubleshooting.md)

### 版本信息
- **当前版本**: v1.0.0
- **Go版本要求**: >= 1.19
- **依赖框架**: Gin, Logrus
- **测试覆盖率**: 90%+

**🎉 快速参考完成！如有问题请查阅完整文档或联系技术支持。**
