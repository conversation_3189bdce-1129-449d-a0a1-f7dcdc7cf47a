package handles

import (
	"io"
	"strconv"
	"time"

	"github.com/alist-org/alist/v3/internal/resume"
	"github.com/alist-org/alist/v3/internal/streaming"
	"github.com/alist-org/alist/v3/server/common"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
)

// StartStreamingUpload 开始流式上传
func StartStreamingUpload(c *gin.Context) {
	log.Info("StartStreamingUpload API called")

	sessionID := c.Param("session_id")
	if sessionID == "" {
		log.Error("Session ID is missing")
		common.ErrorStrResp(c, "Session ID is required", 400)
		return
	}

	log.Infof("Starting streaming upload for session: %s", sessionID)
	
	// 解析配置参数
	config := parseUploadConfig(c)
	
	// 获取请求体作为数据流
	reader := c.Request.Body
	defer reader.Close()

	// 检查全局管理器
	if streaming.GlobalStreamingUploadManager == nil {
		log.Error("GlobalStreamingUploadManager is nil")
		common.ErrorStrResp(c, "Streaming upload manager not initialized", 500)
		return
	}

	// 临时实现：直接处理数据而不使用复杂的流式处理器
	log.Infof("Processing streaming upload for session %s", sessionID)
	err := processStreamingUploadDirect(sessionID, reader, config)
	if err != nil {
		log.Errorf("Failed to process streaming upload for session %s: %v", sessionID, err)
		common.ErrorResp(c, err, 500)
		return
	}
	
	log.Infof("Started streaming upload for session %s", sessionID)
	
	common.SuccessResp(c, gin.H{
		"message":    "Streaming upload started successfully",
		"session_id": sessionID,
		"config":     config,
		"started_at": time.Now(),
	})
}

// GetStreamingUploadStatus 获取流式上传状态
func GetStreamingUploadStatus(c *gin.Context) {
	sessionID := c.Param("session_id")
	if sessionID == "" {
		common.ErrorStrResp(c, "Session ID is required", 400)
		return
	}
	
	// 获取上传状态
	uploadCtx, err := streaming.GlobalStreamingUploadManager.GetUploadStatus(sessionID)
	if err != nil {
		common.ErrorResp(c, err, 404)
		return
	}
	
	// 获取处理进度
	progress, _ := streaming.GlobalStreamingUploadManager.GetUploadProgress(sessionID)
	
	// 获取吞吐量
	throughput, _ := streaming.GlobalStreamingUploadManager.GetUploadThroughput(sessionID)
	
	response := gin.H{
		"session_id":  uploadCtx.SessionID,
		"status":      uploadCtx.Status,
		"started_at":  uploadCtx.StartTime,
		"progress":    progress,
		"throughput":  throughput,
	}
	
	if uploadCtx.Error != nil {
		response["error"] = uploadCtx.Error.Error()
	}
	
	common.SuccessResp(c, response)
}

// GetStreamingUploadMetrics 获取流式上传详细指标
func GetStreamingUploadMetrics(c *gin.Context) {
	sessionID := c.Param("session_id")
	if sessionID == "" {
		common.ErrorStrResp(c, "Session ID is required", 400)
		return
	}
	
	// 获取处理器指标
	metrics, err := streaming.GlobalStreamingUploadManager.GetUploadMetrics(sessionID)
	if err != nil {
		common.ErrorResp(c, err, 404)
		return
	}
	
	// 计算额外的指标
	var duration time.Duration
	if !metrics.EndTime.IsZero() {
		duration = metrics.EndTime.Sub(metrics.StartTime)
	} else {
		duration = time.Since(metrics.StartTime)
	}
	
	response := gin.H{
		"session_id":           sessionID,
		"total_chunks":         metrics.TotalChunks,
		"processed_chunks":     metrics.ProcessedChunks,
		"failed_chunks":        metrics.FailedChunks,
		"total_bytes":          metrics.TotalBytes,
		"processed_bytes":      metrics.ProcessedBytes,
		"average_chunk_time":   metrics.AverageChunkTime.String(),
		"peak_concurrency":     metrics.PeakConcurrency,
		"current_concurrency":  metrics.CurrentConcurrency,
		"start_time":           metrics.StartTime,
		"end_time":             metrics.EndTime,
		"duration":             duration.String(),
		"throughput_bps":       float64(metrics.ProcessedBytes) / duration.Seconds(),
		"throughput_mbps":      float64(metrics.ProcessedBytes) / duration.Seconds() / (1024 * 1024),
	}
	
	if metrics.TotalChunks > 0 {
		response["success_rate"] = float64(metrics.ProcessedChunks) / float64(metrics.TotalChunks) * 100
		response["failure_rate"] = float64(metrics.FailedChunks) / float64(metrics.TotalChunks) * 100
	}
	
	common.SuccessResp(c, response)
}

// CancelStreamingUpload 取消流式上传
func CancelStreamingUpload(c *gin.Context) {
	sessionID := c.Param("session_id")
	if sessionID == "" {
		common.ErrorStrResp(c, "Session ID is required", 400)
		return
	}
	
	err := streaming.GlobalStreamingUploadManager.CancelUpload(sessionID)
	if err != nil {
		log.Errorf("Failed to cancel streaming upload for session %s: %v", sessionID, err)
		common.ErrorResp(c, err, 500)
		return
	}
	
	log.Infof("Cancelled streaming upload for session %s", sessionID)
	
	common.SuccessResp(c, gin.H{
		"message":      "Streaming upload cancelled successfully",
		"session_id":   sessionID,
		"cancelled_at": time.Now(),
	})
}

// ListActiveStreamingUploads 列出所有活跃的流式上传
func ListActiveStreamingUploads(c *gin.Context) {
	activeUploads := streaming.GlobalStreamingUploadManager.GetActiveUploads()
	
	// 转换为响应格式
	uploads := make([]gin.H, 0, len(activeUploads))
	for sessionID, uploadCtx := range activeUploads {
		upload := gin.H{
			"session_id": sessionID,
			"status":     uploadCtx.Status,
			"started_at": uploadCtx.StartTime,
		}
		
		if uploadCtx.Error != nil {
			upload["error"] = uploadCtx.Error.Error()
		}
		
		// 尝试获取进度信息
		if progress, err := streaming.GlobalStreamingUploadManager.GetUploadProgress(sessionID); err == nil {
			upload["progress"] = progress
		}
		
		uploads = append(uploads, upload)
	}
	
	common.SuccessResp(c, gin.H{
		"active_uploads": uploads,
		"count":          len(uploads),
		"timestamp":      time.Now(),
	})
}

// StreamingUploadConfig 流式上传配置API
func StreamingUploadConfig(c *gin.Context) {
	// 获取默认配置
	defaultConfig := streaming.DefaultUploadConfig()
	
	common.SuccessResp(c, gin.H{
		"default_config": gin.H{
			"chunk_size":     defaultConfig.ChunkSize,
			"max_concurrent": defaultConfig.MaxConcurrent,
			"enable_retry":   defaultConfig.EnableRetry,
			"max_retries":    defaultConfig.MaxRetries,
			"timeout":        defaultConfig.Timeout.String(),
		},
		"limits": gin.H{
			"min_chunk_size":     1024,        // 1KB
			"max_chunk_size":     16777216,    // 16MB
			"min_concurrent":     1,
			"max_concurrent":     10,
			"min_timeout":        "1m",
			"max_timeout":        "2h",
		},
	})
}

// TestStreamingUpload 测试流式上传功能
func TestStreamingUpload(c *gin.Context) {
	// 获取测试参数
	sizeStr := c.DefaultQuery("size", "1048576")    // 默认1MB
	chunksStr := c.DefaultQuery("chunks", "4")      // 默认4个分片
	concurrentStr := c.DefaultQuery("concurrent", "2") // 默认2个并发
	
	size, err := strconv.ParseInt(sizeStr, 10, 64)
	if err != nil || size <= 0 {
		common.ErrorStrResp(c, "Invalid size parameter", 400)
		return
	}
	
	chunks, err := strconv.Atoi(chunksStr)
	if err != nil || chunks <= 0 {
		common.ErrorStrResp(c, "Invalid chunks parameter", 400)
		return
	}
	
	concurrent, err := strconv.Atoi(concurrentStr)
	if err != nil || concurrent <= 0 {
		common.ErrorStrResp(c, "Invalid concurrent parameter", 400)
		return
	}
	
	log.Infof("Testing streaming upload with size=%d, chunks=%d, concurrent=%d", 
		size, chunks, concurrent)
	
	// 模拟测试结果
	chunkSize := size / int64(chunks)
	estimatedTime := time.Duration(chunks/concurrent) * 100 * time.Millisecond
	
	common.SuccessResp(c, gin.H{
		"test_config": gin.H{
			"total_size":     size,
			"chunk_count":    chunks,
			"chunk_size":     chunkSize,
			"concurrent":     concurrent,
		},
		"estimated_performance": gin.H{
			"estimated_time":       estimatedTime.String(),
			"estimated_throughput": float64(size) / estimatedTime.Seconds() / (1024 * 1024),
		},
		"message": "Streaming upload test configuration validated",
	})
}

// parseUploadConfig 解析上传配置参数
func parseUploadConfig(c *gin.Context) *streaming.UploadConfig {
	config := streaming.DefaultUploadConfig()
	
	// 解析分片大小
	if chunkSizeStr := c.Query("chunk_size"); chunkSizeStr != "" {
		if chunkSize, err := strconv.ParseInt(chunkSizeStr, 10, 64); err == nil {
			if chunkSize >= 1024 && chunkSize <= 16*1024*1024 { // 1KB - 16MB
				config.ChunkSize = chunkSize
			}
		}
	}
	
	// 解析最大并发数
	if maxConcurrentStr := c.Query("max_concurrent"); maxConcurrentStr != "" {
		if maxConcurrent, err := strconv.Atoi(maxConcurrentStr); err == nil {
			if maxConcurrent >= 1 && maxConcurrent <= 10 {
				config.MaxConcurrent = maxConcurrent
			}
		}
	}
	
	// 解析是否启用重试
	if enableRetryStr := c.Query("enable_retry"); enableRetryStr != "" {
		if enableRetry, err := strconv.ParseBool(enableRetryStr); err == nil {
			config.EnableRetry = enableRetry
		}
	}
	
	// 解析最大重试次数
	if maxRetriesStr := c.Query("max_retries"); maxRetriesStr != "" {
		if maxRetries, err := strconv.Atoi(maxRetriesStr); err == nil {
			if maxRetries >= 0 && maxRetries <= 10 {
				config.MaxRetries = maxRetries
			}
		}
	}
	
	// 解析超时时间
	if timeoutStr := c.Query("timeout"); timeoutStr != "" {
		if timeout, err := time.ParseDuration(timeoutStr); err == nil {
			if timeout >= time.Minute && timeout <= 2*time.Hour {
				config.Timeout = timeout
			}
		}
	}
	
	return config
}

// processStreamingUploadDirect 直接处理流式上传（简化版本）
func processStreamingUploadDirect(sessionID string, reader io.Reader, config *streaming.UploadConfig) error {
	log.Infof("Starting direct streaming upload processing for session %s", sessionID)

	// 获取会话信息
	session, err := resume.GlobalProgressManager.ResumeUploadSession(sessionID)
	if err != nil {
		return err
	}

	log.Infof("Session info: file_size=%d, chunk_size=%d, total_chunks=%d",
		session.FileSize, session.ChunkSize, session.TotalChunks)

	// 读取所有数据
	data, err := io.ReadAll(reader)
	if err != nil {
		return err
	}

	log.Infof("Read %d bytes from request body", len(data))

	// 按分片大小处理数据
	chunkSize := int(session.ChunkSize)
	totalChunks := (len(data) + chunkSize - 1) / chunkSize

	log.Infof("Processing %d bytes in %d chunks of size %d", len(data), totalChunks, chunkSize)

	for i := 0; i < totalChunks; i++ {
		start := i * chunkSize
		end := start + chunkSize
		if end > len(data) {
			end = len(data)
		}

		chunkData := data[start:end]

		log.Infof("Processing chunk %d: %d bytes (offset %d-%d)", i, len(chunkData), start, end-1)

		// 标记分片完成
		err := resume.GlobalProgressManager.MarkChunkCompleted(sessionID, int64(i), chunkData)
		if err != nil {
			log.Errorf("Failed to mark chunk %d completed: %v", i, err)
			return err
		}

		log.Infof("Successfully marked chunk %d as completed", i)
	}

	log.Infof("Direct streaming upload processing completed for session %s", sessionID)
	return nil
}
