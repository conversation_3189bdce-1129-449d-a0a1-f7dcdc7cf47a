package handles

import (
	"fmt"
	"io"
	"strconv"
	"strings"

	"github.com/alist-org/alist/v3/internal/resume"
	"github.com/alist-org/alist/v3/server/common"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
)

// CreateResumableUpload 创建可断点续传的上传会话
func CreateResumableUpload(c *gin.Context) {
	var req struct {
		FileName    string            `json:"file_name" binding:"required"`
		FilePath    string            `json:"file_path" binding:"required"`
		FileSize    int64             `json:"file_size" binding:"required"`
		ChunkSize   int64             `json:"chunk_size"`
		Metadata    map[string]string `json:"metadata"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		common.ErrorResp(c, err, 400)
		return
	}
	
	// 设置默认分片大小
	if req.ChunkSize <= 0 {
		req.ChunkSize = 4 * 1024 * 1024 // 4MB
	}
	
	// 验证文件大小
	if req.FileSize <= 0 {
		common.ErrorStrResp(c, "Invalid file size", 400)
		return
	}
	
	// 获取用户ID（这里简化处理，实际应该从认证中获取）
	userID := c.GetHeader("X-User-ID")
	if userID == "" {
		userID = "anonymous"
	}
	
	// 创建上传会话
	session, err := resume.GlobalProgressManager.CreateUploadSession(
		userID, req.FileName, req.FilePath, req.FileSize, req.ChunkSize, req.Metadata)
	if err != nil {
		log.Errorf("Failed to create upload session: %v", err)
		common.ErrorResp(c, err, 500)
		return
	}
	
	log.Infof("Created resumable upload session %s for user %s, file %s (%d bytes)", 
		session.ID, userID, req.FileName, req.FileSize)
	
	common.SuccessResp(c, gin.H{
		"session_id":    session.ID,
		"file_name":     session.FileName,
		"file_size":     session.FileSize,
		"chunk_size":    session.ChunkSize,
		"total_chunks":  session.TotalChunks,
		"created_at":    session.CreatedAt,
		"expires_at":    session.ExpiresAt,
	})
}

// ResumeUpload 恢复上传会话
func ResumeUpload(c *gin.Context) {
	sessionID := c.Param("session_id")
	if sessionID == "" {
		common.ErrorStrResp(c, "Session ID is required", 400)
		return
	}
	
	session, err := resume.GlobalProgressManager.ResumeUploadSession(sessionID)
	if err != nil {
		log.Errorf("Failed to resume upload session %s: %v", sessionID, err)
		common.ErrorResp(c, err, 404)
		return
	}
	
	// 获取缺失的分片
	missingChunks, err := resume.GlobalProgressManager.GetMissingChunks(sessionID)
	if err != nil {
		log.Errorf("Failed to get missing chunks for session %s: %v", sessionID, err)
		common.ErrorResp(c, err, 500)
		return
	}
	
	log.Infof("Resumed upload session %s, missing chunks: %d", sessionID, len(missingChunks))
	
	common.SuccessResp(c, gin.H{
		"session_id":      session.ID,
		"file_name":       session.FileName,
		"file_size":       session.FileSize,
		"chunk_size":      session.ChunkSize,
		"total_chunks":    session.TotalChunks,
		"uploaded_bytes":  session.UploadedBytes,
		"missing_chunks":  missingChunks,
		"status":          session.Status,
		"created_at":      session.CreatedAt,
		"updated_at":      session.UpdatedAt,
		"expires_at":      session.ExpiresAt,
	})
}

// UploadChunk 上传分片
func UploadChunk(c *gin.Context) {
	sessionID := c.Param("session_id")
	if sessionID == "" {
		common.ErrorStrResp(c, "Session ID is required", 400)
		return
	}
	
	// 获取分片索引
	chunkIndexStr := c.Param("chunk_index")
	chunkIndex, err := strconv.ParseInt(chunkIndexStr, 10, 64)
	if err != nil {
		common.ErrorStrResp(c, "Invalid chunk index", 400)
		return
	}
	
	// 读取分片数据
	chunkData, err := io.ReadAll(c.Request.Body)
	if err != nil {
		log.Errorf("Failed to read chunk data: %v", err)
		common.ErrorResp(c, err, 400)
		return
	}
	
	// 验证分片
	if err := resume.GlobalProgressManager.ValidateChunk(sessionID, chunkIndex, chunkData); err != nil {
		log.Errorf("Chunk validation failed for session %s, chunk %d: %v", sessionID, chunkIndex, err)
		common.ErrorResp(c, err, 400)
		return
	}
	
	// 标记分片完成
	if err := resume.GlobalProgressManager.MarkChunkCompleted(sessionID, chunkIndex, chunkData); err != nil {
		log.Errorf("Failed to mark chunk completed for session %s, chunk %d: %v", sessionID, chunkIndex, err)
		common.ErrorResp(c, err, 500)
		return
	}
	
	// 获取更新后的进度
	progress, err := resume.GlobalProgressManager.GetUploadProgress(sessionID)
	if err != nil {
		log.Errorf("Failed to get upload progress for session %s: %v", sessionID, err)
		common.ErrorResp(c, err, 500)
		return
	}
	
	log.Debugf("Chunk %d uploaded for session %s, progress: %.2f%%", 
		chunkIndex, sessionID, progress.ProgressPercent)
	
	common.SuccessResp(c, gin.H{
		"chunk_index":       chunkIndex,
		"uploaded_bytes":    progress.UploadedBytes,
		"progress_percent":  progress.ProgressPercent,
		"completed_chunks":  progress.CompletedChunks,
		"total_chunks":      progress.TotalChunks,
		"status":            progress.Status,
	})
}

// GetUploadProgress 获取上传进度
func GetUploadProgress(c *gin.Context) {
	sessionID := c.Param("session_id")
	if sessionID == "" {
		common.ErrorStrResp(c, "Session ID is required", 400)
		return
	}
	
	progress, err := resume.GlobalProgressManager.GetUploadProgress(sessionID)
	if err != nil {
		log.Errorf("Failed to get upload progress for session %s: %v", sessionID, err)
		common.ErrorResp(c, err, 404)
		return
	}
	
	common.SuccessResp(c, progress)
}

// ListUserUploads 列出用户的上传会话
func ListUserUploads(c *gin.Context) {
	// 获取用户ID
	userID := c.GetHeader("X-User-ID")
	if userID == "" {
		userID = "anonymous"
	}
	
	sessions, err := resume.GlobalProgressManager.ListUserSessions(userID)
	if err != nil {
		log.Errorf("Failed to list user sessions for user %s: %v", userID, err)
		common.ErrorResp(c, err, 500)
		return
	}
	
	common.SuccessResp(c, gin.H{
		"user_id":  userID,
		"sessions": sessions,
		"count":    len(sessions),
	})
}

// CancelUpload 取消上传
func CancelUpload(c *gin.Context) {
	sessionID := c.Param("session_id")
	if sessionID == "" {
		common.ErrorStrResp(c, "Session ID is required", 400)
		return
	}
	
	if err := resume.GlobalProgressManager.CancelUploadSession(sessionID); err != nil {
		log.Errorf("Failed to cancel upload session %s: %v", sessionID, err)
		common.ErrorResp(c, err, 500)
		return
	}
	
	log.Infof("Cancelled upload session %s", sessionID)
	
	common.SuccessResp(c, gin.H{
		"message":    "Upload cancelled successfully",
		"session_id": sessionID,
	})
}

// DeleteUpload 删除上传会话
func DeleteUpload(c *gin.Context) {
	sessionID := c.Param("session_id")
	if sessionID == "" {
		common.ErrorStrResp(c, "Session ID is required", 400)
		return
	}
	
	if err := resume.GlobalProgressManager.DeleteUploadSession(sessionID); err != nil {
		log.Errorf("Failed to delete upload session %s: %v", sessionID, err)
		common.ErrorResp(c, err, 500)
		return
	}
	
	log.Infof("Deleted upload session %s", sessionID)
	
	common.SuccessResp(c, gin.H{
		"message":    "Upload session deleted successfully",
		"session_id": sessionID,
	})
}

// GetMissingChunks 获取缺失的分片列表
func GetMissingChunks(c *gin.Context) {
	sessionID := c.Param("session_id")
	if sessionID == "" {
		common.ErrorStrResp(c, "Session ID is required", 400)
		return
	}
	
	missingChunks, err := resume.GlobalProgressManager.GetMissingChunks(sessionID)
	if err != nil {
		log.Errorf("Failed to get missing chunks for session %s: %v", sessionID, err)
		common.ErrorResp(c, err, 404)
		return
	}
	
	common.SuccessResp(c, gin.H{
		"session_id":      sessionID,
		"missing_chunks":  missingChunks,
		"missing_count":   len(missingChunks),
	})
}

// UploadStatus 获取上传状态统计
func UploadStatus(c *gin.Context) {
	if resume.GlobalResumeStorage == nil {
		common.ErrorStrResp(c, "Resume storage not initialized", 500)
		return
	}
	
	// 获取存储统计（如果支持）
	if memStorage, ok := resume.GlobalResumeStorage.(*resume.MemoryResumeStorage); ok {
		stats := memStorage.GetSessionStats()
		common.SuccessResp(c, stats)
		return
	}
	
	common.SuccessResp(c, gin.H{
		"message": "Storage statistics not available for this storage type",
	})
}

// ValidateUploadRequest 验证上传请求
func ValidateUploadRequest(c *gin.Context) {
	sessionID := c.Param("session_id")
	if sessionID == "" {
		common.ErrorStrResp(c, "Session ID is required", 400)
		return
	}
	
	// 检查Content-Range头
	contentRange := c.GetHeader("Content-Range")
	if contentRange == "" {
		common.ErrorStrResp(c, "Content-Range header is required", 400)
		return
	}
	
	// 解析Content-Range: bytes start-end/total
	if !strings.HasPrefix(contentRange, "bytes ") {
		common.ErrorStrResp(c, "Invalid Content-Range format", 400)
		return
	}
	
	rangeSpec := strings.TrimPrefix(contentRange, "bytes ")
	parts := strings.Split(rangeSpec, "/")
	if len(parts) != 2 {
		common.ErrorStrResp(c, "Invalid Content-Range format", 400)
		return
	}
	
	rangePart := parts[0]
	totalPart := parts[1]
	
	// 解析范围
	rangeParts := strings.Split(rangePart, "-")
	if len(rangeParts) != 2 {
		common.ErrorStrResp(c, "Invalid range format", 400)
		return
	}
	
	start, err := strconv.ParseInt(rangeParts[0], 10, 64)
	if err != nil {
		common.ErrorStrResp(c, "Invalid start position", 400)
		return
	}
	
	end, err := strconv.ParseInt(rangeParts[1], 10, 64)
	if err != nil {
		common.ErrorStrResp(c, "Invalid end position", 400)
		return
	}
	
	total, err := strconv.ParseInt(totalPart, 10, 64)
	if err != nil {
		common.ErrorStrResp(c, "Invalid total size", 400)
		return
	}
	
	// 验证会话
	session, err := resume.GlobalProgressManager.ResumeUploadSession(sessionID)
	if err != nil {
		common.ErrorResp(c, err, 404)
		return
	}
	
	// 验证文件大小
	if total != session.FileSize {
		common.ErrorStrResp(c, fmt.Sprintf("File size mismatch: expected %d, got %d", 
			session.FileSize, total), 400)
		return
	}
	
	// 验证范围
	if start < 0 || end >= total || start > end {
		common.ErrorStrResp(c, "Invalid byte range", 400)
		return
	}
	
	common.SuccessResp(c, gin.H{
		"valid":      true,
		"session_id": sessionID,
		"start":      start,
		"end":        end,
		"total":      total,
		"chunk_size": end - start + 1,
	})
}
