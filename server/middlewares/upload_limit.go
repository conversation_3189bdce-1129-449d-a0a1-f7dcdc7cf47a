package middlewares

import (
	"context"
	"fmt"
	"time"

	"github.com/alist-org/alist/v3/internal/concurrency"
	"github.com/alist-org/alist/v3/internal/model"
	"github.com/alist-org/alist/v3/server/common"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
)

// UploadConcurrencyLimit 上传并发限制中间件
func UploadConcurrencyLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户信息
		user, exists := c.Get("user")
		if !exists {
			common.ErrorStrResp(c, "User not found", 401)
			c.Abort()
			return
		}
		
		userObj, ok := user.(*model.User)
		if !ok {
			common.ErrorStrResp(c, "Invalid user type", 401)
			c.Abort()
			return
		}
		
		// 检查并发管理器是否已初始化
		if concurrency.GlobalConcurrencyManager == nil {
			log.Warn("Concurrency manager not initialized, skipping upload limit check")
			c.Next()
			return
		}
		
		// 创建带超时的上下文
		ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
		defer cancel()
		
		// 尝试获取上传资源
		userID := userObj.Username
		if userObj.ID != 0 {
			userID = string(rune(userObj.ID)) // 使用用户ID作为标识符
		}
		
		err := concurrency.GlobalConcurrencyManager.AcquireUpload(userID, ctx)
		if err != nil {
			log.Warnf("Upload limit reached for user %s: %v", userID, err)
			common.ErrorResp(c, err, 429) // Too Many Requests
			c.Abort()
			return
		}
		
		// 在请求完成后释放资源
		c.Set("upload_user_id", userID)
		
		// 设置清理函数
		c.Header("X-Upload-Acquired", "true")
		
		c.Next()
		
		// 请求完成后释放资源
		concurrency.GlobalConcurrencyManager.ReleaseUpload(userID)
		log.Debugf("Upload resource released for user %s", userID)
	}
}

// UploadMetrics 获取上传指标的处理器
func UploadMetrics(c *gin.Context) {
	if concurrency.GlobalConcurrencyManager == nil {
		response := gin.H{
			"error": "Concurrency manager not initialized",
			"status": "not_initialized",
			"message": "Upload concurrency control is not available",
		}
		common.ErrorResp(c, fmt.Errorf("concurrency manager not initialized"), 500)
		c.JSON(500, response)
		return
	}

	metrics := concurrency.GlobalConcurrencyManager.GetMetrics()

	response := gin.H{
		"status": "initialized",
		"total_active_uploads": metrics.TotalActiveUploads,
		"queued_uploads":      metrics.QueuedUploads,
		"rejected_uploads":    metrics.RejectedUploads,
		"active_uploads_by_user": metrics.ActiveUploads,
	}

	common.SuccessResp(c, response)
}

// TestConcurrencyControl 测试并发控制的处理器（不需要认证）
func TestConcurrencyControl(c *gin.Context) {
	if concurrency.GlobalConcurrencyManager == nil {
		c.JSON(500, gin.H{
			"error": "Concurrency manager not initialized",
			"status": "not_initialized",
		})
		return
	}

	// 模拟用户ID
	userID := c.Query("user_id")
	if userID == "" {
		userID = "test_user"
	}

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(c.Request.Context(), 2*time.Second)
	defer cancel()

	// 尝试获取上传资源
	err := concurrency.GlobalConcurrencyManager.AcquireUpload(userID, ctx)
	if err != nil {
		c.JSON(429, gin.H{
			"error": err.Error(),
			"status": "limit_reached",
			"user_id": userID,
		})
		return
	}

	// 模拟上传工作
	workDuration := c.Query("duration")
	if workDuration == "" {
		workDuration = "100ms"
	}

	duration, err := time.ParseDuration(workDuration)
	if err != nil {
		duration = 100 * time.Millisecond
	}

	time.Sleep(duration)

	// 释放资源
	concurrency.GlobalConcurrencyManager.ReleaseUpload(userID)

	c.JSON(200, gin.H{
		"status": "success",
		"user_id": userID,
		"duration": workDuration,
		"message": "Upload simulation completed",
	})
}

// GetUserUploadStatus 获取用户上传状态
func GetUserUploadStatus(c *gin.Context) {
	user, exists := c.Get("user")
	if !exists {
		common.ErrorStrResp(c, "User not found", 401)
		return
	}
	
	userObj, ok := user.(*model.User)
	if !ok {
		common.ErrorStrResp(c, "Invalid user type", 401)
		return
	}
	
	if concurrency.GlobalConcurrencyManager == nil {
		common.ErrorStrResp(c, "Concurrency manager not initialized", 500)
		return
	}
	
	userID := userObj.Username
	if userObj.ID != 0 {
		userID = string(rune(userObj.ID))
	}
	
	activeUploads := concurrency.GlobalConcurrencyManager.GetUserActiveUploads(userID)
	globalActiveUploads := concurrency.GlobalConcurrencyManager.GetGlobalActiveUploads()
	
	response := gin.H{
		"user_id":              userID,
		"user_active_uploads":  activeUploads,
		"global_active_uploads": globalActiveUploads,
		"can_upload":           true, // 这里可以添加更复杂的逻辑
	}
	
	common.SuccessResp(c, response)
}
