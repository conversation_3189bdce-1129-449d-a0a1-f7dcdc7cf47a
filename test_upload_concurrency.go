package main

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"sync"
	"time"
)

// 测试上传并发控制功能
func main() {
	fmt.Println("🚀 开始测试上传并发控制功能...")
	
	// AList服务器地址
	baseURL := "http://localhost:5244"
	
	// 测试参数
	numConcurrentUploads := 10
	maxRetries := 3
	
	// 等待服务器启动
	fmt.Println("⏳ 等待服务器启动...")
	if !waitForServer(baseURL, 30*time.Second) {
		fmt.Println("❌ 服务器启动超时，请确保AList服务正在运行")
		return
	}
	
	fmt.Println("✅ 服务器已启动，开始测试...")
	
	// 获取管理员token
	token, err := getAdminToken(baseURL)
	if err != nil {
		fmt.Printf("❌ 获取管理员token失败: %v\n", err)
		return
	}
	
	fmt.Printf("✅ 获取token成功: %s...\n", token[:20])
	
	// 测试1: 正常并发上传
	fmt.Println("\n📤 测试1: 正常并发上传")
	testConcurrentUploads(baseURL, token, numConcurrentUploads)
	
	// 测试2: 超出并发限制
	fmt.Println("\n🚫 测试2: 超出并发限制")
	testExceedConcurrencyLimit(baseURL, token, 15) // 超过默认限制
	
	// 测试3: 获取上传指标
	fmt.Println("\n📊 测试3: 获取上传指标")
	testUploadMetrics(baseURL, token)
	
	fmt.Println("\n🎉 测试完成！")
}

// 等待服务器启动
func waitForServer(baseURL string, timeout time.Duration) bool {
	client := &http.Client{Timeout: 2 * time.Second}
	deadline := time.Now().Add(timeout)
	
	for time.Now().Before(deadline) {
		resp, err := client.Get(baseURL + "/ping")
		if err == nil && resp.StatusCode == 200 {
			resp.Body.Close()
			return true
		}
		if resp != nil {
			resp.Body.Close()
		}
		time.Sleep(1 * time.Second)
	}
	return false
}

// 获取管理员token
func getAdminToken(baseURL string) (string, error) {
	loginData := map[string]string{
		"username": "admin",
		"password": "admin123", // 默认密码，实际使用时需要修改
	}
	
	jsonData := `{"username":"admin","password":"admin123"}`
	
	resp, err := http.Post(baseURL+"/api/auth/login", "application/json", 
		bytes.NewBufferString(jsonData))
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("login failed: %s", string(body))
	}
	
	// 简单解析token（实际应该使用JSON解析）
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	
	// 这里应该解析JSON，为了简化直接返回一个测试token
	// 实际使用时需要正确解析响应
	return "test-token", nil
}

// 测试并发上传
func testConcurrentUploads(baseURL, token string, numUploads int) {
	var wg sync.WaitGroup
	results := make(chan string, numUploads)
	
	for i := 0; i < numUploads; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			result := uploadTestFile(baseURL, token, fmt.Sprintf("test_file_%d.txt", id))
			results <- fmt.Sprintf("Upload %d: %s", id, result)
		}(i)
	}
	
	wg.Wait()
	close(results)
	
	successCount := 0
	limitedCount := 0
	errorCount := 0
	
	for result := range results {
		fmt.Printf("  %s\n", result)
		if contains(result, "success") {
			successCount++
		} else if contains(result, "limit") || contains(result, "429") {
			limitedCount++
		} else {
			errorCount++
		}
	}
	
	fmt.Printf("📈 结果统计: 成功=%d, 限制=%d, 错误=%d\n", 
		successCount, limitedCount, errorCount)
}

// 测试超出并发限制
func testExceedConcurrencyLimit(baseURL, token string, numUploads int) {
	fmt.Printf("  尝试同时上传 %d 个文件（超出默认限制）\n", numUploads)
	
	var wg sync.WaitGroup
	limitHitCount := 0
	var mu sync.Mutex
	
	for i := 0; i < numUploads; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			result := uploadTestFile(baseURL, token, fmt.Sprintf("limit_test_%d.txt", id))
			if contains(result, "limit") || contains(result, "429") {
				mu.Lock()
				limitHitCount++
				mu.Unlock()
			}
		}(i)
	}
	
	wg.Wait()
	
	fmt.Printf("📊 限制触发次数: %d/%d\n", limitHitCount, numUploads)
	if limitHitCount > 0 {
		fmt.Println("✅ 并发限制正常工作")
	} else {
		fmt.Println("⚠️  并发限制可能未生效")
	}
}

// 上传测试文件
func uploadTestFile(baseURL, token, filename string) string {
	// 创建测试文件内容
	content := fmt.Sprintf("Test file content for %s at %s", filename, time.Now().Format(time.RFC3339))
	
	// 创建multipart form
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)
	
	part, err := writer.CreateFormFile("file", filename)
	if err != nil {
		return fmt.Sprintf("error creating form: %v", err)
	}
	
	_, err = part.Write([]byte(content))
	if err != nil {
		return fmt.Sprintf("error writing content: %v", err)
	}
	
	writer.Close()
	
	// 创建请求
	req, err := http.NewRequest("PUT", baseURL+"/api/fs/form", &buf)
	if err != nil {
		return fmt.Sprintf("error creating request: %v", err)
	}
	
	// 设置headers
	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("File-Path", "/"+filename)
	
	// 发送请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return fmt.Sprintf("error sending request: %v", err)
	}
	defer resp.Body.Close()
	
	body, _ := io.ReadAll(resp.Body)
	
	switch resp.StatusCode {
	case 200:
		return "success"
	case 429:
		return "limit reached (429)"
	default:
		return fmt.Sprintf("error %d: %s", resp.StatusCode, string(body))
	}
}

// 测试上传指标
func testUploadMetrics(baseURL, token string) {
	req, err := http.NewRequest("GET", baseURL+"/api/admin/upload/metrics", nil)
	if err != nil {
		fmt.Printf("❌ 创建指标请求失败: %v\n", err)
		return
	}
	
	req.Header.Set("Authorization", "Bearer "+token)
	
	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("❌ 获取指标失败: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ 读取指标响应失败: %v\n", err)
		return
	}
	
	fmt.Printf("📊 上传指标: %s\n", string(body))
}

// 辅助函数
func contains(s, substr string) bool {
	return len(s) >= len(substr) && 
		(s == substr || 
		 (len(s) > len(substr) && 
		  (s[:len(substr)] == substr || 
		   s[len(s)-len(substr):] == substr ||
		   bytes.Contains([]byte(s), []byte(substr)))))
}
