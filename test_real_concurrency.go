package main

import (
	"bytes"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"sync"
	"time"
)

func main() {
	fmt.Println("🚀 开始真实并发上传测试...")
	
	baseURL := "http://localhost:5244"
	
	// 测试1: 检查初始状态
	fmt.Println("📊 检查初始状态...")
	checkMetrics(baseURL)
	
	// 测试2: 并发上传测试（超出限制）
	fmt.Println("\n🔥 测试并发上传（10个并发，超出默认限制5个）...")
	testConcurrentUploads(baseURL, 10)
	
	// 等待一下让上传完成
	time.Sleep(2 * time.Second)
	
	// 测试3: 检查最终状态
	fmt.Println("\n📊 检查最终状态...")
	checkMetrics(baseURL)
	
	fmt.Println("\n🎉 测试完成！")
}

func checkMetrics(baseURL string) {
	resp, err := http.Get(baseURL + "/api/upload/metrics")
	if err != nil {
		fmt.Printf("❌ 获取指标失败: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	body, _ := io.ReadAll(resp.Body)
	fmt.Printf("📊 当前指标: %s\n", string(body))
}

func testConcurrentUploads(baseURL string, numUploads int) {
	var wg sync.WaitGroup
	results := make(chan string, numUploads)
	
	startTime := time.Now()
	
	for i := 0; i < numUploads; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			filename := fmt.Sprintf("test_file_%d.txt", id)
			content := fmt.Sprintf("Test content for file %d at %s", id, time.Now().Format(time.RFC3339))
			
			result := uploadFile(baseURL, filename, content)
			results <- fmt.Sprintf("Upload %d: %s", id, result)
		}(i)
	}
	
	wg.Wait()
	close(results)
	
	duration := time.Since(startTime)
	
	// 统计结果
	successCount := 0
	limitedCount := 0
	errorCount := 0
	
	fmt.Printf("📈 上传结果 (耗时: %v):\n", duration)
	for result := range results {
		fmt.Printf("  %s\n", result)
		
		if contains(result, "success") || contains(result, "200") {
			successCount++
		} else if contains(result, "limit") || contains(result, "429") {
			limitedCount++
		} else {
			errorCount++
		}
	}
	
	fmt.Printf("\n📊 统计结果:\n")
	fmt.Printf("  ✅ 成功: %d\n", successCount)
	fmt.Printf("  🚫 限制: %d\n", limitedCount)
	fmt.Printf("  ❌ 错误: %d\n", errorCount)
	
	if limitedCount > 0 {
		fmt.Printf("🎯 并发控制正常工作！成功限制了 %d 个上传\n", limitedCount)
	} else {
		fmt.Printf("⚠️  没有触发并发限制，可能需要调整测试参数\n")
	}
}

func uploadFile(baseURL, filename, content string) string {
	// 创建multipart form
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)
	
	part, err := writer.CreateFormFile("file", filename)
	if err != nil {
		return fmt.Sprintf("创建表单失败: %v", err)
	}
	
	_, err = part.Write([]byte(content))
	if err != nil {
		return fmt.Sprintf("写入内容失败: %v", err)
	}
	
	writer.Close()
	
	// 创建请求
	req, err := http.NewRequest("PUT", baseURL+"/api/fs/form", &buf)
	if err != nil {
		return fmt.Sprintf("创建请求失败: %v", err)
	}
	
	// 设置headers
	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("File-Path", "/"+filename)
	
	// 发送请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Sprintf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()
	
	body, _ := io.ReadAll(resp.Body)
	
	// 返回详细的状态信息
	bodyStr := string(body)
	if len(bodyStr) > 100 {
		bodyStr = bodyStr[:100] + "..."
	}
	return fmt.Sprintf("状态码 %d: %s", resp.StatusCode, bodyStr)
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && 
		(s == substr || 
		 (len(s) > len(substr) && 
		  (s[:len(substr)] == substr || 
		   s[len(s)-len(substr):] == substr ||
		   bytes.Contains([]byte(s), []byte(substr)))))
}
