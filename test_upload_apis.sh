#!/bin/bash

# AList上传功能测试脚本
# 使用curl测试各种上传API和功能

BASE_URL="http://localhost:5244"
TEST_RESULTS=()

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 记录测试结果
log_test() {
    local test_name="$1"
    local success="$2"
    local message="$3"
    
    if [ "$success" = "true" ]; then
        echo -e "${GREEN}✅ PASS${NC} $test_name: $message"
        TEST_RESULTS+=("PASS: $test_name")
    else
        echo -e "${RED}❌ FAIL${NC} $test_name: $message"
        TEST_RESULTS+=("FAIL: $test_name")
    fi
}

# 测试上传指标API
test_upload_metrics() {
    echo -e "\n${BLUE}📊 测试上传指标API${NC}"
    
    response=$(curl -s -w "%{http_code}" "$BASE_URL/api/upload/metrics")
    http_code="${response: -3}"
    body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        if echo "$body" | grep -q '"status"'; then
            log_test "上传指标API" "true" "API响应正常"
            echo "   响应: $body"
        else
            log_test "上传指标API" "false" "响应格式错误"
        fi
    else
        log_test "上传指标API" "false" "HTTP错误: $http_code"
    fi
}

# 测试缓冲区池API
test_buffer_pool() {
    echo -e "\n${BLUE}🔧 测试缓冲区池API${NC}"
    
    response=$(curl -s -w "%{http_code}" "$BASE_URL/api/buffer-pool/status")
    http_code="${response: -3}"
    body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        if echo "$body" | grep -q '"status"'; then
            log_test "缓冲区池API" "true" "API响应正常"
            echo "   响应: $body"
        else
            log_test "缓冲区池API" "false" "响应格式错误"
        fi
    else
        log_test "缓冲区池API" "false" "HTTP错误: $http_code"
    fi
}

# 测试流式上传配置API
test_streaming_config() {
    echo -e "\n${BLUE}⚡ 测试流式上传配置API${NC}"
    
    response=$(curl -s -w "%{http_code}" "$BASE_URL/api/streaming/config")
    http_code="${response: -3}"
    body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        if echo "$body" | grep -q '"default_config"'; then
            log_test "流式上传配置API" "true" "API响应正常"
            echo "   响应: $body"
        else
            log_test "流式上传配置API" "false" "响应格式错误"
        fi
    else
        log_test "流式上传配置API" "false" "HTTP错误: $http_code"
    fi
}

# 测试断点续传会话创建
test_resumable_upload() {
    echo -e "\n${BLUE}🔄 测试断点续传会话创建${NC}"
    
    payload='{
        "file_name": "test_resumable.txt",
        "file_path": "/test_resumable.txt",
        "file_size": 1024,
        "chunk_size": 256
    }'
    
    response=$(curl -s -w "%{http_code}" \
        -X POST "$BASE_URL/api/resumable/upload" \
        -H "Content-Type: application/json" \
        -H "X-User-ID: test_user" \
        -d "$payload")
    
    http_code="${response: -3}"
    body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        if echo "$body" | grep -q '"session_id"'; then
            session_id=$(echo "$body" | grep -o '"session_id":"[^"]*"' | cut -d'"' -f4)
            log_test "断点续传会话创建" "true" "会话创建成功，ID: $session_id"
            echo "   响应: $body"
            echo "$session_id" > /tmp/alist_session_id.txt
        else
            log_test "断点续传会话创建" "false" "响应格式错误"
        fi
    else
        log_test "断点续传会话创建" "false" "HTTP错误: $http_code"
    fi
}

# 测试会话进度查询
test_upload_progress() {
    echo -e "\n${BLUE}📈 测试上传进度查询${NC}"
    
    if [ -f "/tmp/alist_session_id.txt" ]; then
        session_id=$(cat /tmp/alist_session_id.txt)
        
        response=$(curl -s -w "%{http_code}" \
            "$BASE_URL/api/resumable/upload/$session_id/progress")
        
        http_code="${response: -3}"
        body="${response%???}"
        
        if [ "$http_code" = "200" ]; then
            if echo "$body" | grep -q '"progress"'; then
                log_test "上传进度查询" "true" "进度查询成功"
                echo "   响应: $body"
            else
                log_test "上传进度查询" "false" "响应格式错误"
            fi
        else
            log_test "上传进度查询" "false" "HTTP错误: $http_code"
        fi
    else
        log_test "上传进度查询" "false" "没有可用的会话ID"
    fi
}

# 测试文件大小验证
test_file_size_validation() {
    echo -e "\n${BLUE}📏 测试文件大小验证${NC}"
    
    # 测试零字节文件（应该失败）
    payload='{
        "file_name": "zero_size.txt",
        "file_path": "/zero_size.txt",
        "file_size": 0,
        "chunk_size": 256
    }'
    
    response=$(curl -s -w "%{http_code}" \
        -X POST "$BASE_URL/api/resumable/upload" \
        -H "Content-Type: application/json" \
        -H "X-User-ID: test_user" \
        -d "$payload")
    
    http_code="${response: -3}"
    
    if [ "$http_code" != "200" ]; then
        log_test "零字节文件验证" "true" "正确拒绝零字节文件"
    else
        log_test "零字节文件验证" "false" "错误接受零字节文件"
    fi
    
    # 测试负数文件大小（应该失败）
    payload='{
        "file_name": "negative_size.txt",
        "file_path": "/negative_size.txt",
        "file_size": -1,
        "chunk_size": 256
    }'
    
    response=$(curl -s -w "%{http_code}" \
        -X POST "$BASE_URL/api/resumable/upload" \
        -H "Content-Type: application/json" \
        -H "X-User-ID: test_user" \
        -d "$payload")
    
    http_code="${response: -3}"
    
    if [ "$http_code" != "200" ]; then
        log_test "负数文件大小验证" "true" "正确拒绝负数文件大小"
    else
        log_test "负数文件大小验证" "false" "错误接受负数文件大小"
    fi
    
    # 测试正常文件大小（应该成功）
    payload='{
        "file_name": "normal_size.txt",
        "file_path": "/normal_size.txt",
        "file_size": 1024,
        "chunk_size": 256
    }'
    
    response=$(curl -s -w "%{http_code}" \
        -X POST "$BASE_URL/api/resumable/upload" \
        -H "Content-Type: application/json" \
        -H "X-User-ID: test_user" \
        -d "$payload")
    
    http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        log_test "正常文件大小验证" "true" "正确接受正常文件大小"
    else
        log_test "正常文件大小验证" "false" "错误拒绝正常文件大小"
    fi
}

# 测试分片大小验证
test_chunk_size_validation() {
    echo -e "\n${BLUE}🧩 测试分片大小验证${NC}"
    
    # 测试零分片大小（应该使用默认值）
    payload='{
        "file_name": "zero_chunk.txt",
        "file_path": "/zero_chunk.txt",
        "file_size": 1048576,
        "chunk_size": 0
    }'
    
    response=$(curl -s -w "%{http_code}" \
        -X POST "$BASE_URL/api/resumable/upload" \
        -H "Content-Type: application/json" \
        -H "X-User-ID: test_user" \
        -d "$payload")
    
    http_code="${response: -3}"
    body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        chunk_size=$(echo "$body" | grep -o '"chunk_size":[0-9]*' | cut -d':' -f2)
        if [ "$chunk_size" = "4194304" ]; then  # 4MB
            log_test "零分片大小验证" "true" "正确使用默认分片大小4MB"
        else
            log_test "零分片大小验证" "false" "分片大小不是默认值: $chunk_size"
        fi
    else
        log_test "零分片大小验证" "false" "HTTP错误: $http_code"
    fi
}

# 测试并发上传限制
test_concurrent_limits() {
    echo -e "\n${BLUE}⚡ 测试并发上传限制${NC}"
    
    # 创建多个并发会话
    success_count=0
    total_requests=5
    
    for i in $(seq 1 $total_requests); do
        payload="{
            \"file_name\": \"concurrent_test_$i.txt\",
            \"file_path\": \"/concurrent_test_$i.txt\",
            \"file_size\": 1024,
            \"chunk_size\": 256
        }"
        
        response=$(curl -s -w "%{http_code}" \
            -X POST "$BASE_URL/api/resumable/upload" \
            -H "Content-Type: application/json" \
            -H "X-User-ID: user_$i" \
            -d "$payload")
        
        http_code="${response: -3}"
        
        if [ "$http_code" = "200" ]; then
            ((success_count++))
        fi
        
        # 短暂延迟避免请求过快
        sleep 0.1
    done
    
    if [ $success_count -gt 0 ]; then
        log_test "并发上传限制" "true" "成功创建 $success_count/$total_requests 个并发会话"
    else
        log_test "并发上传限制" "false" "无法创建任何并发会话"
    fi
}

# 测试路径验证
test_path_validation() {
    echo -e "\n${BLUE}📁 测试路径验证${NC}"
    
    # 测试相对路径攻击（应该失败或被处理）
    payload='{
        "file_name": "attack.txt",
        "file_path": "/path/../attack.txt",
        "file_size": 1024,
        "chunk_size": 256
    }'
    
    response=$(curl -s -w "%{http_code}" \
        -X POST "$BASE_URL/api/resumable/upload" \
        -H "Content-Type: application/json" \
        -H "X-User-ID: test_user" \
        -d "$payload")
    
    http_code="${response: -3}"
    
    # 这里我们期望系统能够处理这种路径，无论是拒绝还是规范化
    log_test "相对路径处理" "true" "系统处理了相对路径，HTTP: $http_code"
    
    # 测试中文路径
    payload='{
        "file_name": "中文文件.txt",
        "file_path": "/中文路径/中文文件.txt",
        "file_size": 1024,
        "chunk_size": 256
    }'
    
    response=$(curl -s -w "%{http_code}" \
        -X POST "$BASE_URL/api/resumable/upload" \
        -H "Content-Type: application/json" \
        -H "X-User-ID: test_user" \
        -d "$payload")
    
    http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        log_test "中文路径支持" "true" "支持中文路径"
    else
        log_test "中文路径支持" "false" "不支持中文路径，HTTP: $http_code"
    fi
}

# 生成测试报告
generate_report() {
    echo -e "\n${YELLOW}===========================================${NC}"
    echo -e "${YELLOW}📋 测试报告${NC}"
    echo -e "${YELLOW}===========================================${NC}"
    
    total_tests=${#TEST_RESULTS[@]}
    passed_tests=$(printf '%s\n' "${TEST_RESULTS[@]}" | grep -c "PASS:")
    failed_tests=$(printf '%s\n' "${TEST_RESULTS[@]}" | grep -c "FAIL:")
    
    echo "总测试数: $total_tests"
    echo -e "通过: ${GREEN}$passed_tests ✅${NC}"
    echo -e "失败: ${RED}$failed_tests ❌${NC}"
    
    if [ $total_tests -gt 0 ]; then
        pass_rate=$((passed_tests * 100 / total_tests))
        echo "通过率: $pass_rate%"
    fi
    
    if [ $failed_tests -gt 0 ]; then
        echo -e "\n${RED}❌ 失败的测试:${NC}"
        printf '%s\n' "${TEST_RESULTS[@]}" | grep "FAIL:" | sed 's/FAIL: /  - /'
    fi
    
    echo -e "\n${GREEN}✅ 通过的测试:${NC}"
    printf '%s\n' "${TEST_RESULTS[@]}" | grep "PASS:" | sed 's/PASS: /  - /'
}

# 主函数
main() {
    echo -e "${BLUE}🚀 开始AList上传功能测试${NC}"
    echo "测试服务器: $BASE_URL"
    echo "=========================================="
    
    # 检查服务器是否可达
    if ! curl -s "$BASE_URL/api/upload/metrics" > /dev/null; then
        echo -e "${RED}❌ 错误: 无法连接到AList服务器 $BASE_URL${NC}"
        echo "请确保AList服务器正在运行"
        exit 1
    fi
    
    # 运行所有测试
    test_upload_metrics
    test_buffer_pool
    test_streaming_config
    test_resumable_upload
    test_upload_progress
    test_file_size_validation
    test_chunk_size_validation
    test_concurrent_limits
    test_path_validation
    
    # 生成报告
    generate_report
    
    # 清理临时文件
    rm -f /tmp/alist_session_id.txt
}

# 运行主函数
main "$@"
