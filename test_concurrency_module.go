package main

import (
	"context"
	"fmt"

	"github.com/alist-org/alist/v3/internal/concurrency"
)

func main() {
	fmt.Println("🧪 测试并发控制模块...")
	
	// 初始化并发管理器
	concurrency.InitConcurrencyManager(3, 10)
	fmt.Println("✅ 并发管理器初始化成功")
	
	// 测试获取和释放资源
	ctx := context.Background()
	userID := "test_user"
	
	fmt.Println("📤 测试获取上传资源...")
	err := concurrency.GlobalConcurrencyManager.AcquireUpload(userID, ctx)
	if err != nil {
		fmt.Printf("❌ 获取资源失败: %v\n", err)
		return
	}
	
	fmt.Println("✅ 成功获取上传资源")
	
	// 检查指标
	metrics := concurrency.GlobalConcurrencyManager.GetMetrics()
	fmt.Printf("📊 当前指标: 活跃上传=%d, 用户活跃上传=%d\n", 
		metrics.TotalActiveUploads, metrics.ActiveUploads[userID])
	
	// 释放资源
	concurrency.GlobalConcurrencyManager.ReleaseUpload(userID)
	fmt.Println("✅ 成功释放上传资源")
	
	// 再次检查指标
	finalMetrics := concurrency.GlobalConcurrencyManager.GetMetrics()
	fmt.Printf("📊 最终指标: 活跃上传=%d\n", finalMetrics.TotalActiveUploads)
	
	fmt.Println("🎉 并发控制模块测试完成！")
}
