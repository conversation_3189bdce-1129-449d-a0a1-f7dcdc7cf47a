package main

import (
	"fmt"

	"github.com/alist-org/alist/v3/internal/concurrency"
)

func main() {
	fmt.Println("🔍 检查并发管理器状态...")
	
	if concurrency.GlobalConcurrencyManager == nil {
		fmt.Println("❌ 全局并发管理器未初始化")
		
		// 手动初始化进行测试
		fmt.Println("🔧 手动初始化并发管理器...")
		concurrency.InitConcurrencyManager(5, 50)
		
		if concurrency.GlobalConcurrencyManager != nil {
			fmt.Println("✅ 手动初始化成功")
		} else {
			fmt.Println("❌ 手动初始化失败")
			return
		}
	} else {
		fmt.Println("✅ 全局并发管理器已初始化")
	}
	
	// 测试管理器功能
	fmt.Println("🧪 测试管理器功能...")
	
	metrics := concurrency.GlobalConcurrencyManager.GetMetrics()
	fmt.Printf("📊 当前指标:\n")
	fmt.Printf("  - 全局活跃上传: %d\n", metrics.TotalActiveUploads)
	fmt.Printf("  - 排队上传: %d\n", metrics.QueuedUploads)
	fmt.Printf("  - 被拒绝上传: %d\n", metrics.RejectedUploads)
	fmt.Printf("  - 用户活跃上传: %v\n", metrics.ActiveUploads)
	
	fmt.Println("🎉 检查完成！")
}
